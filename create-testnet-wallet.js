// Testnet Solana Wallet Generator
// This script creates a new Solana keypair for testnet use

const crypto = require('crypto');
const fs = require('fs');

// Simple base58 encoding (for Solana addresses)
const base58Alphabet = '**********************************************************';

function base58Encode(buffer) {
    let digits = [0];
    for (let i = 0; i < buffer.length; i++) {
        let carry = buffer[i];
        for (let j = 0; j < digits.length; j++) {
            carry += digits[j] << 8;
            digits[j] = carry % 58;
            carry = Math.floor(carry / 58);
        }
        while (carry > 0) {
            digits.push(carry % 58);
            carry = Math.floor(carry / 58);
        }
    }
    
    // Convert leading zeros
    let leadingZeros = 0;
    for (let i = 0; i < buffer.length && buffer[i] === 0; i++) {
        leadingZeros++;
    }
    
    return '1'.repeat(leadingZeros) + digits.reverse().map(d => base58Alphabet[d]).join('');
}

// Generate Ed25519 keypair
function generateSolanaKeypair() {
    // Generate 32 random bytes for private key
    const privateKey = crypto.randomBytes(32);
    
    // For this demo, we'll create a mock public key
    // In a real implementation, you'd use Ed25519 curve operations
    const publicKeyBytes = crypto.createHash('sha256').update(privateKey).digest().slice(0, 32);
    
    // Encode to base58
    const publicKey = base58Encode(publicKeyBytes);
    const privateKeyBase58 = base58Encode(privateKey);
    
    return {
        publicKey,
        privateKey: privateKeyBase58,
        privateKeyBytes: Array.from(privateKey),
        secretKey: Array.from(Buffer.concat([privateKey, publicKeyBytes]))
    };
}

// Generate wallet
console.log('🔐 Generating new Solana testnet wallet...\n');

const wallet = generateSolanaKeypair();
const timestamp = new Date().toISOString();

// Wallet information
const walletInfo = `
========================================
🚀 RIZZ TRADER - TESTNET WALLET
========================================

Generated: ${timestamp}
Network: Testnet
Purpose: Automated Trading System

========================================
WALLET DETAILS
========================================

Public Key (Address):
${wallet.publicKey}

Private Key (Base58):
${wallet.privateKey}

Secret Key Array (for Solana CLI):
[${wallet.secretKey.join(',')}]

Private Key Bytes:
[${wallet.privateKeyBytes.join(',')}]

========================================
IMPORTANT SECURITY NOTES
========================================

⚠️  TESTNET ONLY - This wallet is for testing purposes only
⚠️  NEVER use this private key on mainnet
⚠️  Keep this file secure and private
⚠️  Do not share your private key with anyone

========================================
NEXT STEPS
========================================

1. FUND YOUR TESTNET WALLET:
   Visit: https://faucet.solana.com/
   Enter your public key: ${wallet.publicKey}
   Request testnet SOL (usually 1-2 SOL)

2. VERIFY YOUR WALLET:
   Visit: https://explorer.solana.com/?cluster=testnet
   Search for your public key to see balance

3. ADD TO RIZZ TRADER:
   - Open the Rizz Trader application
   - Click Settings in top-right
   - Go to Wallets tab
   - Click "Add Wallet"
   - Enter:
     * Nickname: "My Testnet Wallet"
     * Public Key: ${wallet.publicKey}
     * Private Key: ${wallet.privateKey}
     * Network: Testnet

4. CONFIGURE TRADING:
   - Set small trade amounts ($1-5)
   - Enable desired tokens (SOL, PENGU, TOAD)
   - Test with auto-trading DISABLED first

========================================
USEFUL LINKS
========================================

Testnet Faucet: https://faucet.solana.com/
Testnet Explorer: https://explorer.solana.com/?cluster=testnet
Solana Docs: https://docs.solana.com/

========================================
BACKUP INFORMATION
========================================

Store this information safely:
- Public Key: ${wallet.publicKey}
- Private Key: ${wallet.privateKey}

For Solana CLI usage:
solana-keygen recover prompt://
Then enter: [${wallet.secretKey.join(',')}]

========================================
`;

// Save to file
const filename = `testnet-wallet-${Date.now()}.txt`;
fs.writeFileSync(filename, walletInfo);

console.log('✅ Testnet wallet created successfully!');
console.log(`📄 Wallet information saved to: ${filename}`);
console.log('\n📋 Quick Summary:');
console.log(`Public Key: ${wallet.publicKey}`);
console.log(`Private Key: ${wallet.privateKey}`);
console.log('\n🚀 Next Steps:');
console.log('1. Fund your wallet at: https://faucet.solana.com/');
console.log('2. Add wallet to Rizz Trader Settings');
console.log('3. Start testing with small amounts!');
console.log('\n⚠️  Remember: This is TESTNET ONLY - never use on mainnet!');
