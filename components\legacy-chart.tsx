"use client"

import React, { useState, useEffect, useRef } from 'react'
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Title,
  Tooltip,
  Legend,
} from 'chart.js'
import { Line } from 'react-chartjs-2'
import annotationPlugin from 'chartjs-plugin-annotation'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, TrendingUp, AlertTriangle, Target, BarChart3, Search, BookOpen } from 'lucide-react'

ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Title, Tooltip, Legend, annotationPlugin)

// Token mapping for CoinGecko API
const TOKEN_MAPPING: { [key: string]: string } = {
  PENGU: "pudgy-penguins",
  SOL: "solana",
  TOAD: "toad-network",
  BONK: "bonk",
  WIF: "dogwifcoin",
  JUP: "jupiter-exchange-solana",
  PYTH: "pyth-network",
  JTO: "jito-governance-token",
  RNDR: "render-token",
  HNT: "helium",
  MOBILE: "helium-mobile",
}

interface PriceData {
  prices: number[]
  times: string[]
  currentPrice: number
}

interface BlurbData {
  text: string
  x: number
  y: number
  visible: boolean
}

interface ResearchNote {
  id: string
  token: string
  title: string
  content: string
  timestamp: number
  tags: string[]
}

export default function LegacyChart() {
  const [ticker, setTicker] = useState("PENGU")
  const [priceData, setPriceData] = useState<PriceData | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [blurb, setBlurb] = useState<BlurbData>({ text: "", x: 0, y: 0, visible: false })
  const [researchNotes, setResearchNotes] = useState<ResearchNote[]>([])
  const [newNote, setNewNote] = useState({ title: "", content: "", tags: "" })
  const [showResearch, setShowResearch] = useState(false)
  const chartRef = useRef<ChartJS<"line"> | null>(null)

  const fetchPriceData = async (symbol: string) => {
    setLoading(true)
    setError(null)

    try {
      const coinId = TOKEN_MAPPING[symbol.toUpperCase()] || symbol.toLowerCase()
      console.log(`Fetching data for ${symbol.toUpperCase()} using CoinGecko ID: ${coinId}`)

      const response = await fetch(
        `https://api.coingecko.com/api/v3/coins/${coinId}/market_chart?vs_currency=usd&days=90`,
      )

      if (!response.ok) {
        throw new Error(
          `Token "${symbol}" not found. Using ID: ${coinId}. Try: SOL, BONK, WIF, JUP, PYTH, JTO, RNDR, HNT, MOBILE`,
        )
      }

      const data = await response.json()
      console.log(`Latest price for ${symbol.toUpperCase()}:`, data.prices[data.prices.length - 1][1])

      const prices = data.prices.map((p: [number, number]) => p[1])
      const times = data.prices.map((p: [number, number]) =>
        new Date(p[0]).toLocaleDateString("en-US", { month: "short", day: "numeric" }),
      )

      setPriceData({
        prices,
        times,
        currentPrice: prices[prices.length - 1],
      })
    } catch (err) {
      console.error("API Error:", err)
      setError(err instanceof Error ? err.message : "Failed to fetch data")
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (ticker.trim()) {
      fetchPriceData(ticker.trim())
    }
  }

  const showBlurb = (text: string, event: any) => {
    const rect = event.native?.target?.getBoundingClientRect()
    if (rect) {
      setBlurb({
        text,
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY - 50,
        visible: true,
      })
      setTimeout(() => setBlurb((prev) => ({ ...prev, visible: false })), 3000)
    }
  }

  const addResearchNote = () => {
    if (newNote.title && newNote.content) {
      const note: ResearchNote = {
        id: Date.now().toString(),
        token: ticker.toUpperCase(),
        title: newNote.title,
        content: newNote.content,
        timestamp: Date.now(),
        tags: newNote.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      }
      setResearchNotes([note, ...researchNotes])
      setNewNote({ title: "", content: "", tags: "" })
    }
  }

  const deleteResearchNote = (id: string) => {
    setResearchNotes(researchNotes.filter(note => note.id !== id))
  }

  useEffect(() => {
    fetchPriceData("PENGU")
  }, [])

  const getChartData = () => {
    if (!priceData) return null

    const { prices, times } = priceData
    const midIndex = Math.floor(prices.length * 0.5)
    const boxCenter = prices[midIndex]
    const boxRange = boxCenter * 0.1

    // Calculate 50 EMA
    const ema50Data = prices.map((_, i) => {
      const slice = prices.slice(Math.max(0, i - 49), i + 1)
      return slice.reduce((sum, val) => sum + val, 0) / slice.length
    })

    return {
      labels: times,
      datasets: [
        {
          label: `${ticker.toUpperCase()} Price (USD)`,
          data: prices,
          borderColor: "rgba(75, 192, 192, 1)",
          backgroundColor: "rgba(75, 192, 192, 0.1)",
          fill: false,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 4,
        },
        {
          label: "50 EMA",
          data: ema50Data,
          borderColor: "rgba(0, 255, 0, 0.7)",
          backgroundColor: "rgba(0, 255, 0, 0.1)",
          fill: false,
          borderDash: [5, 5],
          pointRadius: 0,
          pointHoverRadius: 4,
        },
      ],
    }
  }

  const getChartOptions = () => {
    if (!priceData) return {}

    const { prices, times, currentPrice } = priceData
    const midIndex = Math.floor(prices.length * 0.5)
    const boxCenter = prices[midIndex]
    const boxRange = boxCenter * 0.1
    const ema50 = prices.reduce((sum, val, i, arr) => sum + val / Math.min(50, arr.length - i), 0) / 50

    return {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        intersect: false,
        mode: "index" as const,
      },
      scales: {
        x: {
          grid: {
            color: "rgba(0, 0, 0, 0.1)",
          },
        },
        y: {
          beginAtZero: false,
          grid: {
            color: "rgba(0, 0, 0, 0.1)",
          },
          ticks: {
            callback: (value: any) => "$" + value.toFixed(4),
          },
        },
      },
      plugins: {
        legend: {
          position: "top" as const,
        },
        tooltip: {
          enabled: true,
          backgroundColor: "rgba(0, 0, 0, 0.8)",
          titleColor: "white",
          bodyColor: "white",
          borderColor: "rgba(255, 255, 255, 0.2)",
          borderWidth: 1,
          callbacks: {
            label: (context: any) => `${context.dataset.label}: $${context.parsed.y.toFixed(4)}`,
          },
        },
        annotation: {
          annotations: {
            supportResistanceBox: {
              type: "box" as const,
              xMin: times.length * 0.4,
              xMax: times.length * 0.6,
              yMin: boxCenter - boxRange,
              yMax: boxCenter + boxRange,
              backgroundColor: "rgba(255, 165, 0, 0.3)",
              borderColor: "orange",
              borderWidth: 2,
              label: {
                content: "Key Support/Resistance Zone",
                enabled: true,
                position: "center",
                backgroundColor: "rgba(255, 165, 0, 0.8)",
                color: "white",
                padding: 4,
                borderRadius: 4,
              },
              enter: (ctx: any, event: any) => {
                showBlurb(
                  `📊 SUPPORT/RESISTANCE: Entry above $${(boxCenter + boxRange).toFixed(4)} | Exit below $${(boxCenter - boxRange).toFixed(4)}`,
                  event,
                )
              },
            },
            entryPoint: {
              type: "line" as const,
              xMin: times.length * 0.65,
              xMax: times.length * 0.65,
              yMin: boxCenter + boxRange * 0.5,
              yMax: currentPrice,
              borderColor: "blue",
              borderWidth: 3,
              label: {
                content: "Entry Point",
                enabled: true,
                position: "start",
                backgroundColor: "rgba(0, 0, 255, 0.8)",
                color: "white",
                padding: 4,
                borderRadius: 4,
              },
              enter: (ctx: any, event: any) => {
                showBlurb(
                  `🎯 ENTRY: Buy at $${(boxCenter + boxRange * 0.5).toFixed(4)} | Target: $${(currentPrice * 1.1).toFixed(4)} (+10%)`,
                  event,
                )
              },
            },
            rejectionZone: {
              type: "box" as const,
              xMin: 0,
              xMax: times.length,
              yMin: ema50 * 0.95,
              yMax: ema50 * 1.05,
              backgroundColor: "rgba(255, 0, 0, 0.2)",
              borderColor: "red",
              borderWidth: 2,
              label: {
                content: "Rejection Zone",
                enabled: true,
                position: "center",
                backgroundColor: "rgba(255, 0, 0, 0.8)",
                color: "white",
                padding: 4,
                borderRadius: 4,
              },
              enter: (ctx: any, event: any) => {
                showBlurb(
                  `⚠️ REJECTION: Exit below $${ema50.toFixed(4)} | Re-entry above $${(ema50 * 1.05).toFixed(4)}`,
                  event,
                )
              },
            },
            breakoutVector: {
              type: "line" as const,
              xMin: times.length * 0.45,
              xMax: times.length * 0.6,
              yMin: boxCenter - boxRange,
              yMax: boxCenter + boxRange * 1.5,
              borderColor: "green",
              borderWidth: 3,
              borderDash: [10, 5],
              label: {
                content: "Breakout Vector",
                enabled: true,
                position: "start",
                backgroundColor: "rgba(0, 255, 0, 0.8)",
                color: "white",
                padding: 4,
                borderRadius: 4,
              },
              enter: (ctx: any, event: any) => {
                showBlurb(
                  `🚀 BREAKOUT: Entry above $${(boxCenter + boxRange).toFixed(4)} | Target: $${(boxCenter + boxRange * 1.5).toFixed(4)}`,
                  event,
                )
              },
            },
          },
        },
      },
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-slate-800 mb-2 flex items-center gap-2">
              <TrendingUp className="h-8 w-8 text-blue-600" />
              Legacy Chart Analysis
            </h1>
            <p className="text-slate-600">Enhanced chart with manual research capabilities</p>
          </div>
          <Button
            onClick={() => setShowResearch(!showResearch)}
            variant="outline"
            className="flex items-center gap-2"
          >
            <BookOpen className="h-4 w-4" />
            {showResearch ? 'Hide Research' : 'Show Research'}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Chart Section */}
          <div className="lg:col-span-2 space-y-6">
            {/* Chart Container */}
            <Card className="p-6 shadow-lg">
              <div className="h-[600px] relative">
                {loading && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
                    <div className="text-center">
                      <Loader2 className="h-8 w-8 animate-spin mx-auto mb-2 text-blue-600" />
                      <p className="text-slate-600">Loading {ticker.toUpperCase()} data...</p>
                    </div>
                  </div>
                )}

                {error && (
                  <div className="absolute inset-0 flex items-center justify-center bg-white/80 z-10">
                    <div className="text-center text-red-600">
                      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                      <p className="font-medium">{error}</p>
                    </div>
                  </div>
                )}

                {priceData && <Line ref={chartRef} data={getChartData()!} options={getChartOptions()} />}
              </div>
            </Card>

            {/* Input Section */}
            <Card className="p-6 shadow-lg">
              <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 items-center justify-center">
                <div className="flex-1 max-w-md">
                  <label htmlFor="ticker" className="block text-sm font-medium text-slate-700 mb-2">
                    Enter your Solana ticker
                  </label>
                  <Input
                    id="ticker"
                    type="text"
                    value={ticker}
                    onChange={(e) => setTicker(e.target.value)}
                    placeholder="PENGU"
                    className="text-center text-lg font-medium"
                    disabled={loading}
                  />
                  <p className="text-xs text-slate-500 mt-1 text-center">
                    Try: SOL, BONK, WIF, JUP, PYTH, JTO, RNDR, HNT, MOBILE
                  </p>
                </div>
                <Button
                  type="submit"
                  disabled={loading || !ticker.trim()}
                  className="px-8 py-2 bg-blue-600 hover:bg-blue-700"
                >
                  {loading ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Loading...
                    </>
                  ) : (
                    "Load Chart"
                  )}
                </Button>
              </form>
            </Card>
          </div>

          {/* Research Panel */}
          {showResearch && (
            <div className="space-y-6">
              {/* Add Research Note */}
              <Card className="p-4">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Search className="h-5 w-5" />
                    Research Notes
                  </CardTitle>
                </CardHeader>
                <CardContent className="p-0 space-y-4">
                  <Input
                    placeholder="Note title"
                    value={newNote.title}
                    onChange={(e) => setNewNote({ ...newNote, title: e.target.value })}
                  />
                  <textarea
                    className="w-full p-2 border rounded-md resize-none"
                    rows={3}
                    placeholder="Research content..."
                    value={newNote.content}
                    onChange={(e) => setNewNote({ ...newNote, content: e.target.value })}
                  />
                  <Input
                    placeholder="Tags (comma separated)"
                    value={newNote.tags}
                    onChange={(e) => setNewNote({ ...newNote, tags: e.target.value })}
                  />
                  <Button onClick={addResearchNote} className="w-full">
                    Add Note
                  </Button>
                </CardContent>
              </Card>

              {/* Research Notes List */}
              <Card className="p-4">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-lg">Saved Research</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  {researchNotes.length === 0 ? (
                    <p className="text-center text-slate-500 py-4">No research notes yet</p>
                  ) : (
                    <div className="space-y-4 max-h-96 overflow-y-auto">
                      {researchNotes.map((note) => (
                        <div key={note.id} className="border rounded-lg p-3">
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium text-sm">{note.title}</h4>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteResearchNote(note.id)}
                              className="h-6 w-6 p-0"
                            >
                              ×
                            </Button>
                          </div>
                          <p className="text-xs text-slate-600 mb-2">{note.content}</p>
                          <div className="flex flex-wrap gap-1 mb-2">
                            <Badge variant="outline" className="text-xs">{note.token}</Badge>
                            {note.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">{tag}</Badge>
                            ))}
                          </div>
                          <p className="text-xs text-slate-400">
                            {new Date(note.timestamp).toLocaleDateString()}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        {/* Trading Signals Legend */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-8">
          <Card className="p-4 border-l-4 border-orange-500">
            <div className="flex items-center gap-2 mb-2">
              <BarChart3 className="h-5 w-5 text-orange-500" />
              <h3 className="font-semibold text-slate-800">Support/Resistance</h3>
            </div>
            <p className="text-sm text-slate-600">Orange zone shows key price levels where reversals often occur</p>
          </Card>

          <Card className="p-4 border-l-4 border-blue-500">
            <div className="flex items-center gap-2 mb-2">
              <Target className="h-5 w-5 text-blue-500" />
              <h3 className="font-semibold text-slate-800">Entry Point</h3>
            </div>
            <p className="text-sm text-slate-600">Blue line indicates optimal entry after retest confirmation</p>
          </Card>

          <Card className="p-4 border-l-4 border-red-500">
            <div className="flex items-center gap-2 mb-2">
              <AlertTriangle className="h-5 w-5 text-red-500" />
              <h3 className="font-semibold text-slate-800">Rejection Zone</h3>
            </div>
            <p className="text-sm text-slate-600">Red zone around 50 EMA shows potential rejection levels</p>
          </Card>

          <Card className="p-4 border-l-4 border-green-500">
            <div className="flex items-center gap-2 mb-2">
              <TrendingUp className="h-5 w-5 text-green-500" />
              <h3 className="font-semibold text-slate-800">Breakout Vector</h3>
            </div>
            <p className="text-sm text-slate-600">Green line shows projected breakout direction and targets</p>
          </Card>
        </div>

        {/* Current Price Display */}
        {priceData && (
          <div className="text-center mt-6">
            <div className="inline-flex items-center gap-2 bg-slate-800 text-white px-6 py-3 rounded-lg">
              <span className="text-sm font-medium">Current {ticker.toUpperCase()} Price:</span>
              <span className="text-xl font-bold">${priceData.currentPrice.toFixed(4)}</span>
            </div>
          </div>
        )}

        {/* Tooltip Blurb */}
        {blurb.visible && (
          <div
            className="fixed z-50 bg-slate-800 text-white px-3 py-2 rounded-lg shadow-lg text-sm max-w-xs pointer-events-none"
            style={{
              left: blurb.x,
              top: blurb.y,
              transform: "translateX(-50%)",
            }}
          >
            {blurb.text}
          </div>
        )}
      </div>
    </div>
  )
}
