// Client-side config management using localStorage

export interface WalletConfig {
  id: string
  nickname: string
  publicKey: string
  type: 'testnet' | 'mainnet'
  lastFour: string
  isActive: boolean
  secretKey?: string // Only stored for testnet wallets
}

export interface TradingConfig {
  maxUsdPerTrade: number
  tokens: string[]
  autoTrade: boolean
  minProfitThreshold: number
  maxSlippage: number
  gasThreshold: number
}

export interface SignalsConfig {
  supportResistanceRange: number
  emaLength: number
  breakoutThreshold: number
  minProbability: number
}

export interface AppConfig {
  mode: 'testnet' | 'mainnet'
  trading: TradingConfig
  wallets: {
    testnet: WalletConfig[]
    mainnet: WalletConfig[]
  }
  selectedWallet: string | null
  rpc: {
    testnet: string
    mainnet: string
  }
  api: {
    coingecko: string
    solanaFm: string
  }
  signals: SignalsConfig
}

class ConfigManager {
  private configPath: string
  private defaultConfigPath: string
  private config: AppConfig | null = null

  constructor() {
    this.configPath = path.join(process.cwd(), 'config', 'config.json')
    this.defaultConfigPath = path.join(process.cwd(), 'config', 'default-config.json')
  }

  async loadConfig(): Promise<AppConfig> {
    try {
      // Try to load existing config
      const configData = await fs.readFile(this.configPath, 'utf-8')
      this.config = JSON.parse(configData)
      return this.config!
    } catch (error) {
      // If config doesn't exist, create from default
      console.log('Config not found, creating from default...')
      return await this.createDefaultConfig()
    }
  }

  async createDefaultConfig(): Promise<AppConfig> {
    try {
      const defaultConfigData = await fs.readFile(this.defaultConfigPath, 'utf-8')
      const defaultConfig = JSON.parse(defaultConfigData)
      
      // Ensure config directory exists
      await fs.mkdir(path.dirname(this.configPath), { recursive: true })
      
      // Save as user config
      await fs.writeFile(this.configPath, JSON.stringify(defaultConfig, null, 2))
      
      this.config = defaultConfig
      return defaultConfig
    } catch (error) {
      throw new Error(`Failed to create default config: ${error}`)
    }
  }

  async saveConfig(config: AppConfig): Promise<void> {
    try {
      await fs.mkdir(path.dirname(this.configPath), { recursive: true })
      await fs.writeFile(this.configPath, JSON.stringify(config, null, 2))
      this.config = config
    } catch (error) {
      throw new Error(`Failed to save config: ${error}`)
    }
  }

  async updateConfig(updates: Partial<AppConfig>): Promise<AppConfig> {
    const currentConfig = await this.loadConfig()
    const updatedConfig = { ...currentConfig, ...updates }
    await this.saveConfig(updatedConfig)
    return updatedConfig
  }

  async addWallet(wallet: WalletConfig): Promise<AppConfig> {
    const config = await this.loadConfig()
    const walletType = wallet.type
    
    // Check if wallet already exists
    const existingWallet = config.wallets[walletType].find(w => w.publicKey === wallet.publicKey)
    if (existingWallet) {
      throw new Error('Wallet already exists')
    }
    
    config.wallets[walletType].push(wallet)
    await this.saveConfig(config)
    return config
  }

  async removeWallet(walletId: string): Promise<AppConfig> {
    const config = await this.loadConfig()
    
    // Remove from both testnet and mainnet
    config.wallets.testnet = config.wallets.testnet.filter(w => w.id !== walletId)
    config.wallets.mainnet = config.wallets.mainnet.filter(w => w.id !== walletId)
    
    // If this was the selected wallet, clear selection
    if (config.selectedWallet === walletId) {
      config.selectedWallet = null
    }
    
    await this.saveConfig(config)
    return config
  }

  async setSelectedWallet(walletId: string): Promise<AppConfig> {
    const config = await this.loadConfig()
    
    // Verify wallet exists
    const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]
    const wallet = allWallets.find(w => w.id === walletId)
    
    if (!wallet) {
      throw new Error('Wallet not found')
    }
    
    config.selectedWallet = walletId
    await this.saveConfig(config)
    return config
  }

  async setMode(mode: 'testnet' | 'mainnet'): Promise<AppConfig> {
    const config = await this.loadConfig()
    config.mode = mode
    
    // Clear selected wallet if it doesn't match the new mode
    if (config.selectedWallet) {
      const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]
      const selectedWallet = allWallets.find(w => w.id === config.selectedWallet)
      
      if (selectedWallet && selectedWallet.type !== mode) {
        config.selectedWallet = null
      }
    }
    
    await this.saveConfig(config)
    return config
  }

  getConfig(): AppConfig | null {
    return this.config
  }

  getCurrentRpcUrl(): string {
    if (!this.config) {
      throw new Error('Config not loaded')
    }
    return this.config.rpc[this.config.mode]
  }

  getSelectedWallet(): WalletConfig | null {
    if (!this.config || !this.config.selectedWallet) {
      return null
    }
    
    const allWallets = [...this.config.wallets.testnet, ...this.config.wallets.mainnet]
    return allWallets.find(w => w.id === this.config!.selectedWallet) || null
  }
}

export const configManager = new ConfigManager()
export default configManager
