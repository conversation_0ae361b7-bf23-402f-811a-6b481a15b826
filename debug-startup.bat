@echo off
echo ========================================
echo    DEBUG STARTUP SCRIPT
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Testing Node.js...
node --version
echo Node.js exit code: %errorlevel%
echo.

echo Testing npm...
npm --version
echo npm exit code: %errorlevel%
echo.

echo Checking if package.json exists...
if exist "package.json" (
    echo package.json found
) else (
    echo package.json NOT found
)
echo.

echo Directory contents:
dir /b
echo.

echo PATH variable:
echo %PATH%
echo.

echo Press any key to exit...
pause >nul
