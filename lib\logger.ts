import { promises as fs } from 'fs'
import path from 'path'

export interface TradeLog {
  id: string
  timestamp: string
  token: string
  type: 'BUY' | 'SELL'
  amount: number
  price: number
  usdValue: number
  txHash: string
  gasUsed: number
  gasCost: number
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
  wallet: string
  signal?: {
    type: string
    probability: number
    indicators: Record<string, any>
  }
}

export interface PositionLog {
  id: string
  token: string
  entryTimestamp: string
  exitTimestamp?: string
  entryPrice: number
  exitPrice?: number
  amount: number
  entryUsdValue: number
  exitUsdValue?: number
  netProfit?: number
  profitPercentage?: number
  status: 'OPEN' | 'CLOSED' | 'FAILED'
  entryTxHash: string
  exitTxHash?: string
  wallet: string
  entrySignal?: {
    type: string
    probability: number
    indicators: Record<string, any>
  }
  exitSignal?: {
    type: string
    probability: number
    indicators: Record<string, any>
  }
}

export interface DebugLog {
  timestamp: string
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG'
  message: string
  data?: any
  error?: string
}

class Logger {
  private logsDir: string
  private tradesFile: string
  private positionsFile: string
  private debugFile: string

  constructor() {
    this.logsDir = path.join(process.cwd(), 'logs')
    this.tradesFile = path.join(this.logsDir, 'trades.json')
    this.positionsFile = path.join(this.logsDir, 'positions.json')
    this.debugFile = path.join(this.logsDir, 'debug.json')
  }

  private async ensureLogsDir(): Promise<void> {
    try {
      await fs.mkdir(this.logsDir, { recursive: true })
    } catch (error) {
      console.error('Failed to create logs directory:', error)
    }
  }

  private async readJsonFile<T>(filePath: string): Promise<T[]> {
    try {
      const data = await fs.readFile(filePath, 'utf-8')
      return JSON.parse(data)
    } catch (error) {
      return []
    }
  }

  private async writeJsonFile<T>(filePath: string, data: T[]): Promise<void> {
    await this.ensureLogsDir()
    await fs.writeFile(filePath, JSON.stringify(data, null, 2))
  }

  async logTrade(trade: TradeLog): Promise<void> {
    try {
      const trades = await this.readJsonFile<TradeLog>(this.tradesFile)
      trades.push(trade)
      await this.writeJsonFile(this.tradesFile, trades)
      
      // Also log to debug
      await this.debug('Trade logged', { trade })
    } catch (error) {
      console.error('Failed to log trade:', error)
    }
  }

  async logPosition(position: PositionLog): Promise<void> {
    try {
      const positions = await this.readJsonFile<PositionLog>(this.positionsFile)
      
      // Check if position already exists (for updates)
      const existingIndex = positions.findIndex(p => p.id === position.id)
      
      if (existingIndex >= 0) {
        positions[existingIndex] = position
      } else {
        positions.push(position)
      }
      
      await this.writeJsonFile(this.positionsFile, positions)
      
      // Also log to debug
      await this.debug('Position logged', { position })
    } catch (error) {
      console.error('Failed to log position:', error)
    }
  }

  async updateTradeStatus(txHash: string, status: 'SUCCESS' | 'FAILED'): Promise<void> {
    try {
      const trades = await this.readJsonFile<TradeLog>(this.tradesFile)
      const tradeIndex = trades.findIndex(t => t.txHash === txHash)
      
      if (tradeIndex >= 0) {
        trades[tradeIndex].status = status
        await this.writeJsonFile(this.tradesFile, trades)
      }
    } catch (error) {
      console.error('Failed to update trade status:', error)
    }
  }

  async closePosition(positionId: string, exitData: {
    exitTimestamp: string
    exitPrice: number
    exitUsdValue: number
    exitTxHash: string
    exitSignal?: any
  }): Promise<void> {
    try {
      const positions = await this.readJsonFile<PositionLog>(this.positionsFile)
      const positionIndex = positions.findIndex(p => p.id === positionId)
      
      if (positionIndex >= 0) {
        const position = positions[positionIndex]
        position.exitTimestamp = exitData.exitTimestamp
        position.exitPrice = exitData.exitPrice
        position.exitUsdValue = exitData.exitUsdValue
        position.exitTxHash = exitData.exitTxHash
        position.exitSignal = exitData.exitSignal
        position.status = 'CLOSED'
        
        // Calculate profit
        position.netProfit = exitData.exitUsdValue - position.entryUsdValue
        position.profitPercentage = (position.netProfit / position.entryUsdValue) * 100
        
        await this.writeJsonFile(this.positionsFile, positions)
        
        await this.info('Position closed', { 
          positionId, 
          profit: position.netProfit,
          profitPercentage: position.profitPercentage 
        })
      }
    } catch (error) {
      console.error('Failed to close position:', error)
    }
  }

  async getTrades(): Promise<TradeLog[]> {
    return await this.readJsonFile<TradeLog>(this.tradesFile)
  }

  async getPositions(): Promise<PositionLog[]> {
    return await this.readJsonFile<PositionLog>(this.positionsFile)
  }

  async getOpenPositions(): Promise<PositionLog[]> {
    const positions = await this.getPositions()
    return positions.filter(p => p.status === 'OPEN')
  }

  async getClosedPositions(): Promise<PositionLog[]> {
    const positions = await this.getPositions()
    return positions.filter(p => p.status === 'CLOSED')
  }

  async getTotalProfit(): Promise<number> {
    const closedPositions = await this.getClosedPositions()
    return closedPositions.reduce((total, position) => total + (position.netProfit || 0), 0)
  }

  async getDebugLogs(): Promise<DebugLog[]> {
    return await this.readJsonFile<DebugLog>(this.debugFile)
  }

  private async logDebug(level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG', message: string, data?: any, error?: string): Promise<void> {
    try {
      const logs = await this.readJsonFile<DebugLog>(this.debugFile)
      
      const logEntry: DebugLog = {
        timestamp: new Date().toISOString(),
        level,
        message,
        data,
        error
      }
      
      logs.push(logEntry)
      
      // Keep only last 1000 debug logs to prevent file from growing too large
      if (logs.length > 1000) {
        logs.splice(0, logs.length - 1000)
      }
      
      await this.writeJsonFile(this.debugFile, logs)
      
      // Also log to console
      console.log(`[${level}] ${message}`, data ? data : '', error ? error : '')
    } catch (err) {
      console.error('Failed to write debug log:', err)
    }
  }

  async info(message: string, data?: any): Promise<void> {
    await this.logDebug('INFO', message, data)
  }

  async warn(message: string, data?: any): Promise<void> {
    await this.logDebug('WARN', message, data)
  }

  async error(message: string, error?: any, data?: any): Promise<void> {
    const errorString = error instanceof Error ? error.message : String(error)
    await this.logDebug('ERROR', message, data, errorString)
  }

  async debug(message: string, data?: any): Promise<void> {
    await this.logDebug('DEBUG', message, data)
  }
}

export const logger = new Logger()
export default logger
