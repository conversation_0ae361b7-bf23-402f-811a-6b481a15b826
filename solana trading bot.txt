using the system of indicators and the instructions - create an automated system that gets all my balances of solana wallets
tells me when to enter and when we will exit
gives me probability over 90% on entry and exit 
and makes the trade if the tx gas fee and it will increase my balance
auto trades solana with starting balance of 5 dollars

create a new ux that has the legacy chart and indicators and info cards and a way to do manual research
in a new tab that is on startup it will show the open positions, closed positions, profit net per position and totals from the stored logs and current transactions using the solana wallets
config button lets us set our seed/wallet that it will use 
 clicking confirm will use it and store it for use in trades
 
 try to use as few api services that require api keys 
 we will need the wallet txs displayed in a seperate tab so we can see the solana balance and other tokens held by our account and any transactions 
 
 in our config it should be set to testnet mode and have an option for live mode 
 what happens is it will switch to using the keys for the solana wallet we selected
 or the test wallet we created 
 make sure they are easily distinguishable in the config modal