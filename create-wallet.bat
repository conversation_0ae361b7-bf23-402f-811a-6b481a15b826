@echo off
echo ========================================
echo   🔐 TESTNET WALLET GENERATOR
echo ========================================
echo.

echo Generating new Solana testnet wallet...
echo.

node create-testnet-wallet.js

echo.
echo ========================================
echo   WALLET CREATED SUCCESSFULLY!
echo ========================================
echo.
echo The wallet information has been saved to a text file.
echo.
echo NEXT STEPS:
echo 1. Fund your wallet at: https://faucet.solana.com/
echo 2. Add the wallet to Rizz Trader Settings
echo 3. Start testing!
echo.
echo Press any key to exit...
pause >nul
