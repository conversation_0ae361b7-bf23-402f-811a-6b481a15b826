"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Settings, 
  Wallet, 
  Plus, 
  Trash2, 
  Eye, 
  EyeOff, 
  Save,
  AlertTriangle,
  TestTube,
  Globe
} from 'lucide-react'

interface WalletConfig {
  id: string
  nickname: string
  publicKey: string
  privateKey?: string
  type: 'testnet' | 'mainnet'
  isSelected: boolean
}

interface TradingConfig {
  autoTrade: boolean
  maxUsdPerTrade: number
  stopLossPercentage: number
  takeProfitPercentage: number
  maxOpenPositions: number
  tradingInterval: number
}

interface SignalsConfig {
  minProbability: number
  enabledTokens: string[]
  rsiOverbought: number
  rsiOversold: number
  emaShort: number
  emaLong: number
}

interface AppConfig {
  mode: 'testnet' | 'mainnet'
  wallets: WalletConfig[]
  trading: TradingConfig
  signals: SignalsConfig
}

interface ConfigModalProps {
  children: React.ReactNode
}

export default function ConfigModal({ children }: ConfigModalProps) {
  const [open, setOpen] = useState(false)
  const [config, setConfig] = useState<AppConfig | null>(null)
  const [newWallet, setNewWallet] = useState({ nickname: '', publicKey: '', privateKey: '', type: 'testnet' as const })
  const [showPrivateKeys, setShowPrivateKeys] = useState<{ [key: string]: boolean }>({})
  const [saving, setSaving] = useState(false)

  // Load config on mount
  useEffect(() => {
    loadConfig()
  }, [])

  const loadConfig = async () => {
    try {
      // Mock config - will be replaced with real config manager
      const mockConfig: AppConfig = {
        mode: 'testnet',
        wallets: [
          {
            id: '1',
            nickname: 'Main Testnet',
            publicKey: 'ABC123...XYZ789',
            privateKey: 'hidden_private_key_1',
            type: 'testnet',
            isSelected: true
          },
          {
            id: '2',
            nickname: 'Trading Wallet',
            publicKey: 'DEF456...UVW012',
            type: 'mainnet',
            isSelected: false
          }
        ],
        trading: {
          autoTrade: false,
          maxUsdPerTrade: 5.0,
          stopLossPercentage: 5.0,
          takeProfitPercentage: 10.0,
          maxOpenPositions: 3,
          tradingInterval: 30
        },
        signals: {
          minProbability: 0.85,
          enabledTokens: ['SOL', 'PENGU', 'TOAD'],
          rsiOverbought: 70,
          rsiOversold: 30,
          emaShort: 12,
          emaLong: 26
        }
      }
      setConfig(mockConfig)
    } catch (error) {
      console.error('Failed to load config:', error)
    }
  }

  const saveConfig = async () => {
    if (!config) return
    
    setSaving(true)
    try {
      // Mock save - will be replaced with real config manager
      console.log('Saving config:', config)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      setOpen(false)
    } catch (error) {
      console.error('Failed to save config:', error)
    } finally {
      setSaving(false)
    }
  }

  const addWallet = () => {
    if (!config || !newWallet.nickname || !newWallet.publicKey) return

    const wallet: WalletConfig = {
      id: Date.now().toString(),
      nickname: newWallet.nickname,
      publicKey: newWallet.publicKey,
      privateKey: newWallet.type === 'testnet' ? newWallet.privateKey : undefined,
      type: newWallet.type,
      isSelected: false
    }

    setConfig({
      ...config,
      wallets: [...config.wallets, wallet]
    })

    setNewWallet({ nickname: '', publicKey: '', privateKey: '', type: 'testnet' })
  }

  const removeWallet = (walletId: string) => {
    if (!config) return

    setConfig({
      ...config,
      wallets: config.wallets.filter(w => w.id !== walletId)
    })
  }

  const selectWallet = (walletId: string) => {
    if (!config) return

    setConfig({
      ...config,
      wallets: config.wallets.map(w => ({
        ...w,
        isSelected: w.id === walletId
      }))
    })
  }

  const togglePrivateKeyVisibility = (walletId: string) => {
    setShowPrivateKeys(prev => ({
      ...prev,
      [walletId]: !prev[walletId]
    }))
  }

  const updateTradingConfig = (updates: Partial<TradingConfig>) => {
    if (!config) return

    setConfig({
      ...config,
      trading: { ...config.trading, ...updates }
    })
  }

  const updateSignalsConfig = (updates: Partial<SignalsConfig>) => {
    if (!config) return

    setConfig({
      ...config,
      signals: { ...config.signals, ...updates }
    })
  }

  const toggleToken = (token: string) => {
    if (!config) return

    const enabledTokens = config.signals.enabledTokens.includes(token)
      ? config.signals.enabledTokens.filter(t => t !== token)
      : [...config.signals.enabledTokens, token]

    updateSignalsConfig({ enabledTokens })
  }

  if (!config) return null

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Configuration Settings
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="general" className="space-y-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="wallets">Wallets</TabsTrigger>
            <TabsTrigger value="trading">Trading</TabsTrigger>
            <TabsTrigger value="signals">Signals</TabsTrigger>
          </TabsList>

          <TabsContent value="general" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Application Mode</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-1">
                    <Label>Network Mode</Label>
                    <p className="text-sm text-slate-600">
                      Switch between testnet (safe testing) and mainnet (real trading)
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <TestTube className="h-4 w-4" />
                    <Switch
                      checked={config.mode === 'mainnet'}
                      onCheckedChange={(checked) => setConfig({ ...config, mode: checked ? 'mainnet' : 'testnet' })}
                    />
                    <Globe className="h-4 w-4" />
                  </div>
                </div>
                <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600" />
                    <p className="text-sm text-yellow-800">
                      <strong>Warning:</strong> Mainnet mode uses real SOL and tokens. Always test on testnet first!
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="wallets" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Wallet Management</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Add New Wallet */}
                <div className="border rounded-lg p-4 space-y-4">
                  <h4 className="font-medium">Add New Wallet</h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="nickname">Nickname</Label>
                      <Input
                        id="nickname"
                        value={newWallet.nickname}
                        onChange={(e) => setNewWallet({ ...newWallet, nickname: e.target.value })}
                        placeholder="My Trading Wallet"
                      />
                    </div>
                    <div>
                      <Label htmlFor="type">Type</Label>
                      <Select value={newWallet.type} onValueChange={(value: 'testnet' | 'mainnet') => setNewWallet({ ...newWallet, type: value })}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="testnet">Testnet</SelectItem>
                          <SelectItem value="mainnet">Mainnet</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="publicKey">Public Key</Label>
                    <Input
                      id="publicKey"
                      value={newWallet.publicKey}
                      onChange={(e) => setNewWallet({ ...newWallet, publicKey: e.target.value })}
                      placeholder="Enter wallet public key"
                    />
                  </div>
                  {newWallet.type === 'testnet' && (
                    <div>
                      <Label htmlFor="privateKey">Private Key (Testnet Only)</Label>
                      <Input
                        id="privateKey"
                        type="password"
                        value={newWallet.privateKey}
                        onChange={(e) => setNewWallet({ ...newWallet, privateKey: e.target.value })}
                        placeholder="Enter private key for testnet wallet"
                      />
                    </div>
                  )}
                  <Button onClick={addWallet} className="w-full">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Wallet
                  </Button>
                </div>

                {/* Existing Wallets */}
                <div className="space-y-3">
                  <h4 className="font-medium">Existing Wallets</h4>
                  {config.wallets.map((wallet) => (
                    <div key={wallet.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center gap-2">
                          <Badge variant={wallet.type === 'testnet' ? 'secondary' : 'default'}>
                            {wallet.type}
                          </Badge>
                          <span className="font-medium">{wallet.nickname}</span>
                          {wallet.isSelected && (
                            <Badge variant="outline">Selected</Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => selectWallet(wallet.id)}
                            disabled={wallet.isSelected}
                          >
                            Select
                          </Button>
                          <Button
                            variant="destructive"
                            size="sm"
                            onClick={() => removeWallet(wallet.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div>
                          <Label className="text-xs">Public Key</Label>
                          <p className="font-mono text-sm">{wallet.publicKey}</p>
                        </div>
                        {wallet.privateKey && (
                          <div>
                            <div className="flex items-center justify-between">
                              <Label className="text-xs">Private Key</Label>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => togglePrivateKeyVisibility(wallet.id)}
                              >
                                {showPrivateKeys[wallet.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                              </Button>
                            </div>
                            <p className="font-mono text-sm">
                              {showPrivateKeys[wallet.id] ? wallet.privateKey : '••••••••••••••••'}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="trading" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Trading Parameters</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <Label>Auto Trading</Label>
                    <p className="text-sm text-slate-600">Enable automated trade execution</p>
                  </div>
                  <Switch
                    checked={config.trading.autoTrade}
                    onCheckedChange={(checked) => updateTradingConfig({ autoTrade: checked })}
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="maxUsd">Max USD per Trade</Label>
                    <Input
                      id="maxUsd"
                      type="number"
                      step="0.1"
                      value={config.trading.maxUsdPerTrade}
                      onChange={(e) => updateTradingConfig({ maxUsdPerTrade: parseFloat(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="maxPositions">Max Open Positions</Label>
                    <Input
                      id="maxPositions"
                      type="number"
                      value={config.trading.maxOpenPositions}
                      onChange={(e) => updateTradingConfig({ maxOpenPositions: parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="stopLoss">Stop Loss %</Label>
                    <Input
                      id="stopLoss"
                      type="number"
                      step="0.1"
                      value={config.trading.stopLossPercentage}
                      onChange={(e) => updateTradingConfig({ stopLossPercentage: parseFloat(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="takeProfit">Take Profit %</Label>
                    <Input
                      id="takeProfit"
                      type="number"
                      step="0.1"
                      value={config.trading.takeProfitPercentage}
                      onChange={(e) => updateTradingConfig({ takeProfitPercentage: parseFloat(e.target.value) })}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="interval">Trading Interval (seconds)</Label>
                  <Input
                    id="interval"
                    type="number"
                    value={config.trading.tradingInterval}
                    onChange={(e) => updateTradingConfig({ tradingInterval: parseInt(e.target.value) })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="signals" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Signal Configuration</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="minProb">Minimum Signal Probability</Label>
                  <Input
                    id="minProb"
                    type="number"
                    step="0.01"
                    min="0"
                    max="1"
                    value={config.signals.minProbability}
                    onChange={(e) => updateSignalsConfig({ minProbability: parseFloat(e.target.value) })}
                  />
                  <p className="text-sm text-slate-600 mt-1">
                    Only execute trades with signals above this probability (0.0 - 1.0)
                  </p>
                </div>

                <div>
                  <Label>Enabled Tokens</Label>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {['SOL', 'PENGU', 'TOAD', 'BONK', 'WIF', 'JUP'].map((token) => (
                      <Button
                        key={token}
                        variant={config.signals.enabledTokens.includes(token) ? 'default' : 'outline'}
                        size="sm"
                        onClick={() => toggleToken(token)}
                      >
                        {token}
                      </Button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="rsiOverbought">RSI Overbought</Label>
                    <Input
                      id="rsiOverbought"
                      type="number"
                      value={config.signals.rsiOverbought}
                      onChange={(e) => updateSignalsConfig({ rsiOverbought: parseInt(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="rsiOversold">RSI Oversold</Label>
                    <Input
                      id="rsiOversold"
                      type="number"
                      value={config.signals.rsiOversold}
                      onChange={(e) => updateSignalsConfig({ rsiOversold: parseInt(e.target.value) })}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="emaShort">EMA Short Period</Label>
                    <Input
                      id="emaShort"
                      type="number"
                      value={config.signals.emaShort}
                      onChange={(e) => updateSignalsConfig({ emaShort: parseInt(e.target.value) })}
                    />
                  </div>
                  <div>
                    <Label htmlFor="emaLong">EMA Long Period</Label>
                    <Input
                      id="emaLong"
                      type="number"
                      value={config.signals.emaLong}
                      onChange={(e) => updateSignalsConfig({ emaLong: parseInt(e.target.value) })}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button variant="outline" onClick={() => setOpen(false)}>
            Cancel
          </Button>
          <Button onClick={saveConfig} disabled={saving}>
            {saving ? (
              <>
                <Save className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Configuration
              </>
            )}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
}
