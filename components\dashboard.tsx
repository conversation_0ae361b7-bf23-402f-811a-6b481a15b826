"use client"

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Activity, 
  Target,
  AlertTriangle,
  Play,
  Pause,
  Settings
} from 'lucide-react'

interface Position {
  id: string
  token: string
  type: 'LONG' | 'SHORT'
  entryPrice: number
  currentPrice: number
  amount: number
  usdValue: number
  unrealizedPnL: number
  profitPercentage: number
  timestamp: number
  status: 'OPEN' | 'CLOSED'
}

interface ClosedPosition {
  id: string
  token: string
  entryPrice: number
  exitPrice: number
  amount: number
  entryUsdValue: number
  exitUsdValue: number
  netProfit: number
  profitPercentage: number
  entryTimestamp: number
  exitTimestamp: number
}

interface TokenCard {
  symbol: string
  name: string
  price: number
  change24h: number
  signal: 'BUY' | 'SELL' | 'HOLD'
  probability: number
  hasPosition: boolean
}

export default function Dashboard() {
  const [openPositions, setOpenPositions] = useState<Position[]>([])
  const [closedPositions, setClosedPositions] = useState<ClosedPosition[]>([])
  const [tokenCards, setTokenCards] = useState<TokenCard[]>([])
  const [isAutoTrading, setIsAutoTrading] = useState(false)
  const [totalStats, setTotalStats] = useState({
    totalValue: 0,
    totalUnrealizedPnL: 0,
    totalRealizedPnL: 0,
    winRate: 0
  })

  // Mock data - will be replaced with real API calls
  useEffect(() => {
    // Mock open positions
    setOpenPositions([
      {
        id: '1',
        token: 'SOL',
        type: 'LONG',
        entryPrice: 95.50,
        currentPrice: 98.20,
        amount: 0.052,
        usdValue: 5.11,
        unrealizedPnL: 0.14,
        profitPercentage: 2.83,
        timestamp: Date.now() - 3600000,
        status: 'OPEN'
      },
      {
        id: '2',
        token: 'PENGU',
        type: 'LONG',
        entryPrice: 0.032,
        currentPrice: 0.034,
        amount: 156.25,
        usdValue: 5.31,
        unrealizedPnL: 0.31,
        profitPercentage: 6.25,
        timestamp: Date.now() - 7200000,
        status: 'OPEN'
      }
    ])

    // Mock closed positions
    setClosedPositions([
      {
        id: '3',
        token: 'TOAD',
        entryPrice: 0.0045,
        exitPrice: 0.0048,
        amount: 1111.11,
        entryUsdValue: 5.00,
        exitUsdValue: 5.33,
        netProfit: 0.33,
        profitPercentage: 6.67,
        entryTimestamp: Date.now() - 86400000,
        exitTimestamp: Date.now() - 82800000
      }
    ])

    // Mock token cards
    setTokenCards([
      {
        symbol: 'SOL',
        name: 'Solana',
        price: 98.20,
        change24h: 2.45,
        signal: 'HOLD',
        probability: 0.85,
        hasPosition: true
      },
      {
        symbol: 'TOAD',
        name: 'Toad Network',
        price: 0.0048,
        change24h: -1.23,
        signal: 'BUY',
        probability: 0.92,
        hasPosition: false
      },
      {
        symbol: 'PENGU',
        name: 'Pudgy Penguins',
        price: 0.034,
        change24h: 5.67,
        signal: 'HOLD',
        probability: 0.78,
        hasPosition: true
      }
    ])

    // Mock total stats
    setTotalStats({
      totalValue: 10.42,
      totalUnrealizedPnL: 0.45,
      totalRealizedPnL: 0.33,
      winRate: 100
    })
  }, [])

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(value)
  }

  const formatPercentage = (value: number) => {
    return `${value >= 0 ? '+' : ''}${value.toFixed(2)}%`
  }

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  const getSignalColor = (signal: string) => {
    switch (signal) {
      case 'BUY': return 'bg-green-500'
      case 'SELL': return 'bg-red-500'
      default: return 'bg-gray-500'
    }
  }

  const getPnLColor = (value: number) => {
    return value >= 0 ? 'text-green-600' : 'text-red-600'
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-slate-800 mb-2 flex items-center gap-2">
              <Activity className="h-8 w-8 text-blue-600" />
              Rizz Trader Dashboard
            </h1>
            <p className="text-slate-600">Automated Solana Trading System</p>
          </div>
          <div className="flex gap-4">
            <Button
              onClick={() => setIsAutoTrading(!isAutoTrading)}
              variant={isAutoTrading ? "destructive" : "default"}
              className="flex items-center gap-2"
            >
              {isAutoTrading ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              {isAutoTrading ? 'Stop Trading' : 'Start Trading'}
            </Button>
            <Button variant="outline" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Config
            </Button>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Portfolio Value</CardTitle>
              <DollarSign className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(totalStats.totalValue)}</div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Unrealized P&L</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPnLColor(totalStats.totalUnrealizedPnL)}`}>
                {formatCurrency(totalStats.totalUnrealizedPnL)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Realized P&L</CardTitle>
              <Target className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getPnLColor(totalStats.totalRealizedPnL)}`}>
                {formatCurrency(totalStats.totalRealizedPnL)}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Win Rate</CardTitle>
              <AlertTriangle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{totalStats.winRate}%</div>
            </CardContent>
          </Card>
        </div>

        {/* Token Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {tokenCards.map((token) => (
            <Card key={token.symbol} className="relative">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">{token.symbol}</CardTitle>
                    <p className="text-sm text-slate-600">{token.name}</p>
                  </div>
                  <div className="flex flex-col items-end gap-2">
                    <Badge className={getSignalColor(token.signal)}>
                      {token.signal}
                    </Badge>
                    {token.hasPosition && (
                      <Badge variant="outline">POSITION</Badge>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Price:</span>
                    <span className="font-medium">{formatCurrency(token.price)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">24h Change:</span>
                    <span className={getPnLColor(token.change24h)}>
                      {formatPercentage(token.change24h)}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-slate-600">Signal Confidence:</span>
                    <span className="font-medium">{(token.probability * 100).toFixed(1)}%</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Positions Tabs */}
        <Tabs defaultValue="open" className="space-y-4">
          <TabsList>
            <TabsTrigger value="open">Open Positions ({openPositions.length})</TabsTrigger>
            <TabsTrigger value="closed">Closed Positions ({closedPositions.length})</TabsTrigger>
          </TabsList>

          <TabsContent value="open" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Open Positions</CardTitle>
              </CardHeader>
              <CardContent>
                {openPositions.length === 0 ? (
                  <p className="text-center text-slate-500 py-8">No open positions</p>
                ) : (
                  <div className="space-y-4">
                    {openPositions.map((position) => (
                      <div key={position.id} className="border rounded-lg p-4">
                        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                          <div>
                            <p className="text-sm text-slate-600">Token</p>
                            <p className="font-medium">{position.token}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Entry Price</p>
                            <p className="font-medium">{formatCurrency(position.entryPrice)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Current Price</p>
                            <p className="font-medium">{formatCurrency(position.currentPrice)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Amount</p>
                            <p className="font-medium">{position.amount.toFixed(6)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Unrealized P&L</p>
                            <p className={`font-medium ${getPnLColor(position.unrealizedPnL)}`}>
                              {formatCurrency(position.unrealizedPnL)} ({formatPercentage(position.profitPercentage)})
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Opened</p>
                            <p className="font-medium text-xs">{formatTime(position.timestamp)}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="closed" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Closed Positions</CardTitle>
              </CardHeader>
              <CardContent>
                {closedPositions.length === 0 ? (
                  <p className="text-center text-slate-500 py-8">No closed positions</p>
                ) : (
                  <div className="space-y-4">
                    {closedPositions.map((position) => (
                      <div key={position.id} className="border rounded-lg p-4">
                        <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
                          <div>
                            <p className="text-sm text-slate-600">Token</p>
                            <p className="font-medium">{position.token}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Entry → Exit</p>
                            <p className="font-medium text-xs">
                              {formatCurrency(position.entryPrice)} → {formatCurrency(position.exitPrice)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Amount</p>
                            <p className="font-medium">{position.amount.toFixed(6)}</p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Net Profit</p>
                            <p className={`font-medium ${getPnLColor(position.netProfit)}`}>
                              {formatCurrency(position.netProfit)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Profit %</p>
                            <p className={`font-medium ${getPnLColor(position.profitPercentage)}`}>
                              {formatPercentage(position.profitPercentage)}
                            </p>
                          </div>
                          <div>
                            <p className="text-sm text-slate-600">Duration</p>
                            <p className="font-medium text-xs">
                              {Math.round((position.exitTimestamp - position.entryTimestamp) / 3600000)}h
                            </p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}
