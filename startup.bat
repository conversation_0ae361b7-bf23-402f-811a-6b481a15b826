@echo off
echo ========================================
echo    🚀 RIZZ TRADER SYSTEM STARTUP 🚀
echo ========================================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

:: Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: npm is not installed or not in PATH
    echo Please install Node.js which includes npm
    echo.
    pause
    exit /b 1
)

echo ✅ Node.js and npm are installed
echo.

:: Check if package.json exists
if not exist "package.json" (
    echo ❌ ERROR: package.json not found
    echo Please run this script from the project root directory
    echo.
    pause
    exit /b 1
)

echo 📦 Installing dependencies...
echo.
npm install

if %errorlevel% neq 0 (
    echo ❌ ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Dependencies installed successfully
echo.

:: Check if .env.local exists, if not create a template
if not exist ".env.local" (
    echo 📝 Creating .env.local template...
    echo # Rizz Trader System Environment Variables > .env.local
    echo # >> .env.local
    echo # Solana Configuration >> .env.local
    echo NEXT_PUBLIC_SOLANA_NETWORK=testnet >> .env.local
    echo NEXT_PUBLIC_RPC_ENDPOINT=https://api.testnet.solana.com >> .env.local
    echo # >> .env.local
    echo # CoinGecko API (Free tier - no key required) >> .env.local
    echo NEXT_PUBLIC_COINGECKO_API_URL=https://api.coingecko.com/api/v3 >> .env.local
    echo # >> .env.local
    echo # Trading Configuration >> .env.local
    echo NEXT_PUBLIC_AUTO_TRADE_ENABLED=false >> .env.local
    echo NEXT_PUBLIC_MAX_USD_PER_TRADE=5.0 >> .env.local
    echo # >> .env.local
    echo # Logging >> .env.local
    echo NEXT_PUBLIC_LOG_LEVEL=info >> .env.local
    echo.
    echo ✅ Created .env.local template
    echo ⚠️  Please review and update the environment variables as needed
    echo.
)

:: Create logs directory if it doesn't exist
if not exist "logs" (
    echo 📁 Creating logs directory...
    mkdir logs
    echo ✅ Logs directory created
    echo.
)

:: Create data directory if it doesn't exist
if not exist "data" (
    echo 📁 Creating data directory...
    mkdir data
    echo ✅ Data directory created
    echo.
)

:: Check if config file exists, if not copy default
if not exist "config\config.json" (
    if exist "config\default-config.json" (
        echo 📋 Creating initial config from default...
        copy "config\default-config.json" "config\config.json" >nul
        echo ✅ Initial config created
        echo.
    ) else (
        echo ⚠️  Warning: No default config found
        echo.
    )
)

echo 🔧 Building the application...
echo.
npm run build

if %errorlevel% neq 0 (
    echo ❌ ERROR: Build failed
    echo Please check the error messages above
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ Build completed successfully
echo.

echo 🌐 Starting the Rizz Trader System...
echo.
echo 📊 Dashboard will be available at: http://localhost:3000
echo 📈 Legacy Chart will be available at: http://localhost:3000 (Chart tab)
echo 💰 Wallet Management will be available at: http://localhost:3000 (Wallet tab)
echo.
echo ⚠️  IMPORTANT NOTES:
echo    • The system starts in TESTNET mode by default
echo    • Configure your wallets in the Settings before trading
echo    • Always test on testnet before using mainnet
echo    • Auto-trading is DISABLED by default for safety
echo.
echo 🚀 Launching application...
echo.

:: Start the Next.js application
start "" "http://localhost:3000"
npm run start

:: If we get here, the server stopped
echo.
echo 🛑 Rizz Trader System has stopped
echo.
pause
