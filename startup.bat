@echo off
echo ========================================
echo    RIZZ TRADER SYSTEM STARTUP
echo ========================================
echo.
echo Starting diagnostic checks...
echo.

:: Show current directory
echo Current directory: %CD%
echo.

:: Check if Node.js is installed
echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js from https://nodejs.org/
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
) else (
    echo Node.js version:
    node --version
)

:: Check if npm is installed
echo.
echo Checking npm installation...
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm is not installed or not in PATH
    echo Please install Node.js which includes npm
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
) else (
    echo npm version:
    npm --version
)

echo.
echo Node.js and npm are installed successfully
echo.

:: Check if package.json exists
echo Checking for package.json...
if not exist "package.json" (
    echo ERROR: package.json not found in current directory
    echo Please run this script from the project root directory
    echo Current directory contents:
    dir /b
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
) else (
    echo Found package.json
)

echo.
echo Installing dependencies...
echo This may take a few minutes...
echo.
npm install

if %errorlevel% neq 0 (
    echo ERROR: Failed to install dependencies
    echo Please check your internet connection and try again
    echo Error level: %errorlevel%
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo Dependencies installed successfully
echo.

:: Check if .env.local exists, if not create a template
echo Checking for .env.local file...
if not exist ".env.local" (
    echo Creating .env.local template...
    echo # Rizz Trader System Environment Variables > .env.local
    echo # >> .env.local
    echo # Solana Configuration >> .env.local
    echo NEXT_PUBLIC_SOLANA_NETWORK=testnet >> .env.local
    echo NEXT_PUBLIC_RPC_ENDPOINT=https://api.testnet.solana.com >> .env.local
    echo # >> .env.local
    echo # CoinGecko API (Free tier - no key required) >> .env.local
    echo NEXT_PUBLIC_COINGECKO_API_URL=https://api.coingecko.com/api/v3 >> .env.local
    echo # >> .env.local
    echo # Trading Configuration >> .env.local
    echo NEXT_PUBLIC_AUTO_TRADE_ENABLED=false >> .env.local
    echo NEXT_PUBLIC_MAX_USD_PER_TRADE=5.0 >> .env.local
    echo # >> .env.local
    echo # Logging >> .env.local
    echo NEXT_PUBLIC_LOG_LEVEL=info >> .env.local
    echo.
    echo Created .env.local template
    echo Please review and update the environment variables as needed
    echo.
) else (
    echo Found existing .env.local file
)

:: Create logs directory if it doesn't exist
echo Checking/creating logs directory...
if not exist "logs" (
    mkdir logs
    echo Created logs directory
) else (
    echo Logs directory already exists
)

:: Create data directory if it doesn't exist
echo Checking/creating data directory...
if not exist "data" (
    mkdir data
    echo Created data directory
) else (
    echo Data directory already exists
)

:: Check if config file exists, if not copy default
echo Checking configuration files...
if not exist "config\config.json" (
    if exist "config\default-config.json" (
        echo Creating initial config from default...
        copy "config\default-config.json" "config\config.json" >nul
        echo Initial config created
    ) else (
        echo Warning: No default config found
        echo This may cause issues - please check config directory
    )
) else (
    echo Found existing config.json
)

echo.
echo Building the application...
echo This may take several minutes...
echo.
npm run build

if %errorlevel% neq 0 (
    echo ERROR: Build failed
    echo Please check the error messages above
    echo Error level: %errorlevel%
    echo.
    echo Press any key to exit...
    pause >nul
    exit /b 1
)

echo.
echo Build completed successfully
echo.

echo Starting the Rizz Trader System...
echo.
echo Dashboard will be available at: http://localhost:3000
echo Legacy Chart will be available at: http://localhost:3000 (Chart tab)
echo Wallet Management will be available at: http://localhost:3000 (Wallet tab)
echo.
echo IMPORTANT NOTES:
echo   - The system starts in TESTNET mode by default
echo   - Configure your wallets in the Settings before trading
echo   - Always test on testnet before using mainnet
echo   - Auto-trading is DISABLED by default for safety
echo.
echo Launching application...
echo.

:: Start the Next.js application
echo Opening browser to http://localhost:3000
start "" "http://localhost:3000"

echo Starting Next.js server...
npm run start

:: If we get here, the server stopped
echo.
echo Rizz Trader System has stopped
echo.
echo Press any key to exit...
pause >nul
