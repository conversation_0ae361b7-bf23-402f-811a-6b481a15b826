@echo off
echo ========================================
echo    SIMPLE RIZZ TRADER STARTUP
echo ========================================
echo.

echo Step 1: Checking Node.js and npm...
echo.

:: Check Node.js
echo Checking Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found in PATH
    echo Please install Node.js from https://nodejs.org/
    goto :error_exit
)

node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js command failed
    goto :error_exit
)

:: Check npm
echo.
echo Checking npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: npm not found in PATH
    echo Please reinstall Node.js which includes npm
    goto :error_exit
)

npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm command failed
    goto :error_exit
)

echo.
echo Step 2: Checking project files...
if not exist "package.json" (
    echo ERROR: package.json not found
    echo Current directory: %CD%
    echo Please run this from the project root directory
    goto :error_exit
)

echo Found package.json
echo.

echo Step 3: Installing dependencies...
echo This may take a few minutes...
npm install
if %errorlevel% neq 0 (
    echo ERROR: npm install failed
    goto :error_exit
)

echo.
echo Step 4: Starting development server...
echo Opening browser...
start "" "http://localhost:3000"

echo Starting Next.js...
npm run dev

goto :end

:error_exit
echo.
echo ========================================
echo An error occurred. Please check above.
echo ========================================
echo.
pause
exit /b 1

:end
echo.
echo Server stopped.
pause
