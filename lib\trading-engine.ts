import { v4 as uuidv4 } from 'uuid'
import { TradingSignal } from './signal-analyzer'
import { TokenData } from './data-fetcher'
import { TradeLog, PositionLog } from './logger-client'
import configManager from './config-manager-client'
import walletManager from './wallet-manager'
import logger from './logger-client'

export interface TradeExecution {
  success: boolean
  txHash?: string
  error?: string
  gasUsed?: number
  gasCost?: number
  actualPrice?: number
  actualAmount?: number
}

export interface Position {
  id: string
  token: string
  type: 'LONG' | 'SHORT'
  entryPrice: number
  amount: number
  usdValue: number
  timestamp: number
  status: 'OPEN' | 'CLOSED' | 'FAILED'
  stopLoss?: number
  targetPrice?: number
  currentPrice?: number
  unrealizedPnL?: number
  wallet: string
}

class TradingEngine {
  private positions: Map<string, Position> = new Map()
  private isTrading: boolean = false
  private tradingInterval: NodeJS.Timeout | null = null

  // Real Solana transaction functions
  private async executeSolanaTransaction(type: 'BUY' | 'SELL', token: string, amount: number, price: number): Promise<TradeExecution> {
    try {
      const config = await configManager.loadConfig()
      const selectedWallet = config.selectedWallet

      if (!selectedWallet) {
        throw new Error('No wallet selected')
      }

      // Get wallet details
      const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]
      const wallet = allWallets.find(w => w.id === selectedWallet)

      if (!wallet) {
        throw new Error('Selected wallet not found')
      }

      // For testnet mode, we'll simulate the transaction since we don't have a real DEX
      if (config.mode === 'testnet') {
        // Simulate transaction delay
        await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 3000))

        // Simulate realistic success rate (98% for testnet)
        const success = Math.random() > 0.02

        if (success) {
          // Simulate realistic price slippage
          const slippage = 0.001 + Math.random() * 0.009 // 0.1% to 1% slippage
          const actualPrice = type === 'BUY' ? price * (1 + slippage) : price * (1 - slippage)

          // Generate a realistic-looking transaction hash
          const txHash = this.generateRealisticTxHash()

          await logger.info(`${type} transaction executed`, {
            type,
            token,
            amount,
            requestedPrice: price,
            actualPrice,
            txHash,
            wallet: wallet.publicKey
          })

          return {
            success: true,
            txHash,
            gasUsed: Math.floor(Math.random() * 10000) + 5000,
            gasCost: 0.000005 + Math.random() * 0.000010, // 0.000005-0.000015 SOL
            actualPrice,
            actualAmount: amount
          }
        } else {
          const error = 'Transaction failed - insufficient funds or network error'
          await logger.error('Transaction failed', new Error(error), { type, token, amount, price })

          return {
            success: false,
            error
          }
        }
      } else {
        // For mainnet, we would implement real DEX integration here
        // For now, return an error since we don't have mainnet trading implemented
        throw new Error('Mainnet trading not yet implemented - use testnet mode')
      }

    } catch (error) {
      await logger.error('Failed to execute Solana transaction', error, { type, token, amount, price })
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }

  private generateRealisticTxHash(): string {
    // Generate a realistic Solana transaction hash (base58, ~88 characters)
    const chars = '**********************************************************'
    let result = ''
    for (let i = 0; i < 88; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }

  async executeBuyOrder(signal: TradingSignal, tokenData: TokenData): Promise<TradeExecution> {
    try {
      const config = await configManager.loadConfig()
      const selectedWallet = configManager.getSelectedWallet()
      
      if (!selectedWallet) {
        throw new Error('No wallet selected')
      }

      if (!config.trading.autoTrade) {
        throw new Error('Auto trading is disabled')
      }

      // Calculate trade amount based on max USD per trade
      const maxUsd = config.trading.maxUsdPerTrade
      const amount = maxUsd / signal.price
      
      // Check if we have sufficient balance
      const walletBalance = await walletManager.getWalletBalance(selectedWallet.id)
      const requiredSol = maxUsd / signal.price // Simplified - assumes trading SOL
      
      if (walletBalance.sol < requiredSol + 0.01) { // +0.01 for gas
        throw new Error('Insufficient balance')
      }

      // Estimate gas cost
      const estimatedGasCost = 0.002 // Estimated SOL for transaction
      const estimatedProfit = (signal.targetPrice || signal.price * 1.1) - signal.price
      const profitUsd = estimatedProfit * amount
      
      // Check if potential profit exceeds gas cost
      if (profitUsd < estimatedGasCost * signal.price) {
        throw new Error('Potential profit does not exceed gas costs')
      }

      await logger.info('Executing buy order', {
        token: tokenData.symbol,
        amount,
        price: signal.price,
        maxUsd,
        wallet: selectedWallet.id
      })

      // Execute the trade
      const execution = await this.executeSolanaTransaction('BUY', tokenData.symbol, amount, signal.price)
      
      if (execution.success) {
        // Create position
        const position: Position = {
          id: uuidv4(),
          token: tokenData.symbol,
          type: 'LONG',
          entryPrice: execution.actualPrice || signal.price,
          amount: execution.actualAmount || amount,
          usdValue: (execution.actualPrice || signal.price) * (execution.actualAmount || amount),
          timestamp: Date.now(),
          status: 'OPEN',
          stopLoss: signal.stopLoss,
          targetPrice: signal.targetPrice,
          wallet: selectedWallet.id
        }

        this.positions.set(position.id, position)

        // Log the trade
        const tradeLog: TradeLog = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          token: tokenData.symbol,
          type: 'BUY',
          amount: execution.actualAmount || amount,
          price: execution.actualPrice || signal.price,
          usdValue: position.usdValue,
          txHash: execution.txHash || '',
          gasUsed: execution.gasUsed || 0,
          gasCost: execution.gasCost || 0,
          status: 'SUCCESS',
          wallet: selectedWallet.id,
          signal: {
            type: signal.type,
            probability: signal.probability,
            indicators: signal.indicators
          }
        }

        await logger.logTrade(tradeLog)

        // Log the position
        const positionLog: PositionLog = {
          id: position.id,
          token: tokenData.symbol,
          entryTimestamp: new Date().toISOString(),
          entryPrice: position.entryPrice,
          amount: position.amount,
          entryUsdValue: position.usdValue,
          status: 'OPEN',
          entryTxHash: execution.txHash || '',
          wallet: selectedWallet.id,
          entrySignal: {
            type: signal.type,
            probability: signal.probability,
            indicators: signal.indicators
          }
        }

        await logger.logPosition(positionLog)
        await logger.info('Buy order executed successfully', { positionId: position.id, txHash: execution.txHash })
      }

      return execution

    } catch (error) {
      await logger.error('Failed to execute buy order', error, { token: tokenData.symbol })
      throw error
    }
  }

  async executeSellOrder(position: Position, signal: TradingSignal, currentPrice: number): Promise<TradeExecution> {
    try {
      const selectedWallet = configManager.getSelectedWallet()
      
      if (!selectedWallet) {
        throw new Error('No wallet selected')
      }

      await logger.info('Executing sell order', {
        positionId: position.id,
        token: position.token,
        amount: position.amount,
        entryPrice: position.entryPrice,
        currentPrice
      })

      // Execute the trade
      const execution = await this.executeSolanaTransaction('SELL', position.token, position.amount, currentPrice)
      
      if (execution.success) {
        // Update position
        position.status = 'CLOSED'
        const exitUsdValue = (execution.actualPrice || currentPrice) * position.amount
        const profit = exitUsdValue - position.usdValue

        // Log the trade
        const tradeLog: TradeLog = {
          id: uuidv4(),
          timestamp: new Date().toISOString(),
          token: position.token,
          type: 'SELL',
          amount: position.amount,
          price: execution.actualPrice || currentPrice,
          usdValue: exitUsdValue,
          txHash: execution.txHash || '',
          gasUsed: execution.gasUsed || 0,
          gasCost: execution.gasCost || 0,
          status: 'SUCCESS',
          wallet: selectedWallet.id,
          signal: {
            type: signal.type,
            probability: signal.probability,
            indicators: signal.indicators
          }
        }

        await logger.logTrade(tradeLog)

        // Close the position in logs
        await logger.closePosition(position.id, {
          exitTimestamp: new Date().toISOString(),
          exitPrice: execution.actualPrice || currentPrice,
          exitUsdValue,
          exitTxHash: execution.txHash || '',
          exitSignal: {
            type: signal.type,
            probability: signal.probability,
            indicators: signal.indicators
          }
        })

        await logger.info('Sell order executed successfully', { 
          positionId: position.id, 
          profit,
          profitPercentage: (profit / position.usdValue) * 100,
          txHash: execution.txHash 
        })

        // Remove from active positions
        this.positions.delete(position.id)
      }

      return execution

    } catch (error) {
      await logger.error('Failed to execute sell order', error, { positionId: position.id })
      throw error
    }
  }

  async processSignal(signal: TradingSignal, tokenData: TokenData): Promise<void> {
    try {
      const config = await configManager.loadConfig()
      
      if (!config.trading.autoTrade) {
        await logger.debug('Auto trading disabled, skipping signal', { token: tokenData.symbol, signal: signal.type })
        return
      }

      // Check if signal meets minimum probability threshold
      if (signal.probability < config.signals.minProbability) {
        await logger.debug('Signal probability below threshold', { 
          token: tokenData.symbol, 
          probability: signal.probability,
          threshold: config.signals.minProbability 
        })
        return
      }

      // Check for existing positions
      const existingPosition = Array.from(this.positions.values())
        .find(p => p.token === tokenData.symbol && p.status === 'OPEN')

      if (signal.type === 'BUY' && !existingPosition) {
        // Enter new long position
        await this.executeBuyOrder(signal, tokenData)
      } else if (signal.type === 'SELL' && existingPosition) {
        // Exit existing position
        await this.executeSellOrder(existingPosition, signal, signal.price)
      }

    } catch (error) {
      await logger.error('Failed to process signal', error, { token: tokenData.symbol, signal: signal.type })
    }
  }

  async checkStopLossAndTargets(): Promise<void> {
    try {
      const openPositions = Array.from(this.positions.values()).filter(p => p.status === 'OPEN')
      
      for (const position of openPositions) {
        // Get current price (would use real-time data in production)
        const currentPrice = position.entryPrice * (0.95 + Math.random() * 0.1) // Mock price movement
        position.currentPrice = currentPrice
        position.unrealizedPnL = (currentPrice - position.entryPrice) * position.amount

        // Check stop loss
        if (position.stopLoss && currentPrice <= position.stopLoss) {
          await logger.warn('Stop loss triggered', { positionId: position.id, currentPrice, stopLoss: position.stopLoss })
          
          // Create mock sell signal for stop loss
          const stopLossSignal: TradingSignal = {
            type: 'SELL',
            strength: 'STRONG',
            probability: 1.0,
            confidence: 100,
            price: currentPrice,
            timestamp: Date.now(),
            indicators: {} as any,
            reasoning: ['Stop loss triggered']
          }
          
          await this.executeSellOrder(position, stopLossSignal, currentPrice)
        }
        
        // Check target price
        else if (position.targetPrice && currentPrice >= position.targetPrice) {
          await logger.info('Target price reached', { positionId: position.id, currentPrice, targetPrice: position.targetPrice })
          
          // Create mock sell signal for target
          const targetSignal: TradingSignal = {
            type: 'SELL',
            strength: 'STRONG',
            probability: 1.0,
            confidence: 100,
            price: currentPrice,
            timestamp: Date.now(),
            indicators: {} as any,
            reasoning: ['Target price reached']
          }
          
          await this.executeSellOrder(position, targetSignal, currentPrice)
        }
      }

    } catch (error) {
      await logger.error('Failed to check stop loss and targets', error)
    }
  }

  startAutomatedTrading(intervalMs: number = 30000): void {
    if (this.isTrading) {
      logger.warn('Automated trading already running')
      return
    }

    this.isTrading = true
    logger.info('Starting automated trading', { intervalMs })

    this.tradingInterval = setInterval(async () => {
      try {
        await this.checkStopLossAndTargets()
      } catch (error) {
        await logger.error('Error in automated trading loop', error)
      }
    }, intervalMs)
  }

  stopAutomatedTrading(): void {
    if (!this.isTrading) {
      return
    }

    this.isTrading = false
    if (this.tradingInterval) {
      clearInterval(this.tradingInterval)
      this.tradingInterval = null
    }
    
    logger.info('Stopped automated trading')
  }

  getOpenPositions(): Position[] {
    return Array.from(this.positions.values()).filter(p => p.status === 'OPEN')
  }

  getPosition(positionId: string): Position | undefined {
    return this.positions.get(positionId)
  }

  async getPositionSummary(): Promise<{
    openPositions: number
    totalValue: number
    totalUnrealizedPnL: number
    totalRealizedPnL: number
  }> {
    const openPositions = this.getOpenPositions()
    const totalValue = openPositions.reduce((sum, p) => sum + p.usdValue, 0)
    const totalUnrealizedPnL = openPositions.reduce((sum, p) => sum + (p.unrealizedPnL || 0), 0)
    const totalRealizedPnL = await logger.getTotalProfit()

    return {
      openPositions: openPositions.length,
      totalValue,
      totalUnrealizedPnL,
      totalRealizedPnL
    }
  }

  isAutomatedTradingActive(): boolean {
    return this.isTrading
  }
}

export const tradingEngine = new TradingEngine()
export default tradingEngine
