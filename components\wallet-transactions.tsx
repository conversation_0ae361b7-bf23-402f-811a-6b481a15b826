"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Wallet, 
  RefreshCw, 
  ExternalLink, 
  Copy,
  TrendingUp,
  TrendingDown,
  ArrowUpRight,
  ArrowDownLeft,
  Clock
} from 'lucide-react'

interface TokenBalance {
  mint: string
  symbol: string
  name: string
  balance: number
  decimals: number
  uiAmount: number
  usdValue: number
  logoUri?: string
}

interface WalletBalance {
  sol: number
  solUsd: number
  tokens: TokenBalance[]
  totalUsd: number
}

interface Transaction {
  signature: string
  timestamp: number
  type: 'SEND' | 'RECEIVE' | 'SWAP' | 'STAKE' | 'UNSTAKE' | 'UNKNOWN'
  status: 'success' | 'failed' | 'pending'
  amount?: number
  token?: string
  fee: number
  from?: string
  to?: string
  description: string
}

interface WalletInfo {
  id: string
  nickname: string
  publicKey: string
  type: 'testnet' | 'mainnet'
  lastFour: string
  isActive: boolean
}

export default function WalletTransactions() {
  const [selectedWallet, setSelectedWallet] = useState<string>("")
  const [wallets, setWallets] = useState<WalletInfo[]>([])
  const [balance, setBalance] = useState<WalletBalance | null>(null)
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [loading, setLoading] = useState(false)
  const [refreshing, setRefreshing] = useState(false)

  // Load real wallet data from config
  useEffect(() => {
    loadWallets()
  }, [])

  const loadWallets = async () => {
    try {
      // Import wallet manager
      const { default: walletManager } = await import('../lib/wallet-manager')
      const { default: configManager } = await import('../lib/config-manager-client')

      const config = await configManager.loadConfig()
      const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]

      setWallets(allWallets)

      // Set selected wallet from config or first wallet
      const selectedWalletId = config.selectedWallet || allWallets[0]?.id
      if (selectedWalletId) {
        setSelectedWallet(selectedWalletId)
      }
    } catch (error) {
      console.error('Failed to load wallets:', error)
    }
  }

  useEffect(() => {
    if (selectedWallet) {
      loadWalletData(selectedWallet)
    }
  }, [selectedWallet])

  const loadWalletData = async (walletId: string) => {
    setLoading(true)
    try {
      // Import wallet manager
      const { default: walletManager } = await import('../lib/wallet-manager')

      // Get real wallet balance
      const walletBalance = await walletManager.getWalletBalance(walletId)
      setBalance(walletBalance)

      // Get real transaction history
      const walletTransactions = await walletManager.getTransactionHistory(walletId, 50)

      // Convert to the expected format
      const formattedTransactions: Transaction[] = walletTransactions.map(tx => ({
        signature: tx.signature,
        timestamp: tx.timestamp,
        type: tx.type as 'SEND' | 'RECEIVE' | 'SWAP',
        status: tx.status,
        amount: tx.amount || 0,
        token: tx.token || 'SOL',
        fee: 0.000005, // Default fee
        description: `${tx.type} ${tx.token || 'SOL'}`
      }))

      setTransactions(formattedTransactions)
    } catch (error) {
      console.error('Failed to load wallet data:', error)
      // Set empty data on error
      setBalance({
        sol: 0,
        solUsd: 0,
        totalUsd: 0,
        tokens: []
      })
      setTransactions([])
    } finally {
      setLoading(false)
    }
  }

  const refreshWalletData = async () => {
    if (!selectedWallet) return
    setRefreshing(true)
    await loadWalletData(selectedWallet)
    setRefreshing(false)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    // Could add toast notification here
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 4
    }).format(value)
  }

  const formatTokenAmount = (amount: number, decimals: number = 6) => {
    return (amount / Math.pow(10, decimals)).toFixed(decimals)
  }

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleString()
  }

  const getTransactionIcon = (type: string) => {
    switch (type) {
      case 'RECEIVE': return <ArrowDownLeft className="h-4 w-4 text-green-600" />
      case 'SEND': return <ArrowUpRight className="h-4 w-4 text-red-600" />
      case 'SWAP': return <RefreshCw className="h-4 w-4 text-blue-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'bg-green-500'
      case 'failed': return 'bg-red-500'
      case 'pending': return 'bg-yellow-500'
      default: return 'bg-gray-500'
    }
  }

  const selectedWalletInfo = wallets.find(w => w.id === selectedWallet)

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-4xl font-bold text-slate-800 mb-2 flex items-center gap-2">
              <Wallet className="h-8 w-8 text-blue-600" />
              Wallet Transactions
            </h1>
            <p className="text-slate-600">Monitor your Solana wallet balances and transaction history</p>
          </div>
          <div className="flex gap-4">
            <Select value={selectedWallet} onValueChange={setSelectedWallet}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select wallet" />
              </SelectTrigger>
              <SelectContent>
                {wallets.map((wallet) => (
                  <SelectItem key={wallet.id} value={wallet.id}>
                    <div className="flex items-center gap-2">
                      <Badge variant={wallet.type === 'testnet' ? 'secondary' : 'default'}>
                        {wallet.type}
                      </Badge>
                      <span>{wallet.nickname}</span>
                      <span className="text-xs text-slate-500">...{wallet.lastFour}</span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Button
              onClick={refreshWalletData}
              disabled={refreshing || !selectedWallet}
              variant="outline"
              className="flex items-center gap-2"
            >
              <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </div>

        {selectedWalletInfo && (
          <>
            {/* Wallet Info */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <span>Wallet Information</span>
                  <Badge className={getStatusColor('success')}>
                    {selectedWalletInfo.type.toUpperCase()}
                  </Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <p className="text-sm text-slate-600">Nickname</p>
                    <p className="font-medium">{selectedWalletInfo.nickname}</p>
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Public Key</p>
                    <div className="flex items-center gap-2">
                      <p className="font-medium font-mono text-sm">{selectedWalletInfo.publicKey}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(selectedWalletInfo.publicKey)}
                        className="h-6 w-6 p-0"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-slate-600">Status</p>
                    <Badge variant={selectedWalletInfo.isActive ? 'default' : 'secondary'}>
                      {selectedWalletInfo.isActive ? 'Active' : 'Inactive'}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Balance Overview */}
            {balance && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">SOL Balance</CardTitle>
                    <TrendingUp className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{balance.sol.toFixed(4)} SOL</div>
                    <p className="text-xs text-muted-foreground">{formatCurrency(balance.solUsd)}</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Token Count</CardTitle>
                    <TrendingDown className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{balance.tokens.length}</div>
                    <p className="text-xs text-muted-foreground">Different tokens</p>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                    <Wallet className="h-4 w-4 text-muted-foreground" />
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{formatCurrency(balance.totalUsd)}</div>
                    <p className="text-xs text-muted-foreground">USD equivalent</p>
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Tabs for Balances and Transactions */}
            <Tabs defaultValue="balances" className="space-y-4">
              <TabsList>
                <TabsTrigger value="balances">Token Balances</TabsTrigger>
                <TabsTrigger value="transactions">Transaction History</TabsTrigger>
              </TabsList>

              <TabsContent value="balances" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Token Holdings</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {!balance || balance.tokens.length === 0 ? (
                      <p className="text-center text-slate-500 py-8">No tokens found</p>
                    ) : (
                      <div className="space-y-4">
                        {/* SOL Balance */}
                        <div className="border rounded-lg p-4">
                          <div className="grid grid-cols-4 gap-4">
                            <div>
                              <p className="text-sm text-slate-600">Token</p>
                              <p className="font-medium">SOL</p>
                              <p className="text-xs text-slate-500">Solana</p>
                            </div>
                            <div>
                              <p className="text-sm text-slate-600">Balance</p>
                              <p className="font-medium">{balance.sol.toFixed(6)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-slate-600">USD Value</p>
                              <p className="font-medium">{formatCurrency(balance.solUsd)}</p>
                            </div>
                            <div>
                              <p className="text-sm text-slate-600">Actions</p>
                              <Button variant="outline" size="sm">
                                <ExternalLink className="h-3 w-3 mr-1" />
                                Explorer
                              </Button>
                            </div>
                          </div>
                        </div>

                        {/* Token Balances */}
                        {balance.tokens.map((token, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-4 gap-4">
                              <div>
                                <p className="text-sm text-slate-600">Token</p>
                                <p className="font-medium">{token.symbol}</p>
                                <p className="text-xs text-slate-500">{token.name}</p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Balance</p>
                                <p className="font-medium">{token.uiAmount.toFixed(6)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">USD Value</p>
                                <p className="font-medium">{formatCurrency(token.usdValue)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Actions</p>
                                <Button variant="outline" size="sm">
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  Explorer
                                </Button>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="transactions" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Recent Transactions</CardTitle>
                  </CardHeader>
                  <CardContent>
                    {transactions.length === 0 ? (
                      <p className="text-center text-slate-500 py-8">No transactions found</p>
                    ) : (
                      <div className="space-y-4">
                        {transactions.map((tx, index) => (
                          <div key={index} className="border rounded-lg p-4">
                            <div className="grid grid-cols-5 gap-4">
                              <div className="flex items-center gap-2">
                                {getTransactionIcon(tx.type)}
                                <div>
                                  <p className="font-medium text-sm">{tx.type}</p>
                                  <Badge className={getStatusColor(tx.status)} variant="secondary">
                                    {tx.status}
                                  </Badge>
                                </div>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Amount</p>
                                <p className="font-medium">
                                  {tx.amount ? `${tx.amount} ${tx.token}` : '-'}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Fee</p>
                                <p className="font-medium">{tx.fee} SOL</p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Time</p>
                                <p className="font-medium text-xs">{formatTime(tx.timestamp)}</p>
                              </div>
                              <div>
                                <p className="text-sm text-slate-600">Signature</p>
                                <div className="flex items-center gap-1">
                                  <p className="font-mono text-xs truncate max-w-20">
                                    {tx.signature.substring(0, 8)}...
                                  </p>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => copyToClipboard(tx.signature)}
                                    className="h-6 w-6 p-0"
                                  >
                                    <Copy className="h-3 w-3" />
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    className="h-6 w-6 p-0"
                                  >
                                    <ExternalLink className="h-3 w-3" />
                                  </Button>
                                </div>
                              </div>
                            </div>
                            <p className="text-sm text-slate-600 mt-2">{tx.description}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </>
        )}
      </div>
    </div>
  )
}
