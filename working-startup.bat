@echo off
echo ========================================
echo    🚀 RIZZ TRADER SYSTEM STARTUP 🚀
echo ========================================
echo.

echo Node.js version:
node --version
echo.

echo Checking project structure...
if not exist "package.json" (
    echo ERROR: package.json not found in %CD%
    echo Please run this script from the project root directory
    pause
    exit /b 1
)
echo ✅ Found package.json

:: Create necessary directories
if not exist "logs" (
    echo Creating logs directory...
    mkdir logs
)

if not exist "data" (
    echo Creating data directory...
    mkdir data
)

if not exist "config" (
    echo Creating config directory...
    mkdir config
)

:: Create default config if it doesn't exist
if not exist "config\default-config.json" (
    echo Creating default configuration...
    echo { > config\default-config.json
    echo   "mode": "testnet", >> config\default-config.json
    echo   "wallets": [], >> config\default-config.json
    echo   "trading": { >> config\default-config.json
    echo     "maxUsdPerTrade": 5.0, >> config\default-config.json
    echo     "stopLossPercentage": 5, >> config\default-config.json
    echo     "takeProfitPercentage": 10, >> config\default-config.json
    echo     "maxOpenPositions": 3, >> config\default-config.json
    echo     "tradingInterval": 30, >> config\default-config.json
    echo     "autoTradingEnabled": false >> config\default-config.json
    echo   }, >> config\default-config.json
    echo   "signals": { >> config\default-config.json
    echo     "minProbability": 0.85, >> config\default-config.json
    echo     "enabledTokens": ["SOL", "PENGU", "TOAD"], >> config\default-config.json
    echo     "rsiOverbought": 70, >> config\default-config.json
    echo     "rsiOversold": 30, >> config\default-config.json
    echo     "emaShort": 12, >> config\default-config.json
    echo     "emaLong": 26 >> config\default-config.json
    echo   }, >> config\default-config.json
    echo   "rpcEndpoints": { >> config\default-config.json
    echo     "testnet": "https://api.testnet.solana.com", >> config\default-config.json
    echo     "mainnet": "https://api.mainnet-beta.solana.com" >> config\default-config.json
    echo   }, >> config\default-config.json
    echo   "apiUrls": { >> config\default-config.json
    echo     "coingecko": "https://api.coingecko.com/api/v3" >> config\default-config.json
    echo   } >> config\default-config.json
    echo } >> config\default-config.json
)

:: Copy default config to config.json if it doesn't exist
if not exist "config\config.json" (
    if exist "config\default-config.json" (
        echo Creating initial config from default...
        copy "config\default-config.json" "config\config.json" >nul
    )
)

:: Create .env.local if it doesn't exist
if not exist ".env.local" (
    echo Creating .env.local file...
    echo # Rizz Trader System Environment Variables > .env.local
    echo NEXT_PUBLIC_SOLANA_NETWORK=testnet >> .env.local
    echo NEXT_PUBLIC_RPC_ENDPOINT=https://api.testnet.solana.com >> .env.local
    echo NEXT_PUBLIC_COINGECKO_API_URL=https://api.coingecko.com/api/v3 >> .env.local
    echo NEXT_PUBLIC_AUTO_TRADE_ENABLED=false >> .env.local
    echo NEXT_PUBLIC_MAX_USD_PER_TRADE=5.0 >> .env.local
    echo NEXT_PUBLIC_LOG_LEVEL=info >> .env.local
)

echo ✅ Project structure ready
echo.

:: Check if node_modules exists, if not try to install dependencies
if not exist "node_modules" (
    echo Installing dependencies...
    echo This may take a few minutes...
    echo.
    
    :: Try npm first
    npm install >nul 2>&1
    if %errorlevel% neq 0 (
        echo npm failed, trying alternative installation...
        
        :: Try using node directly to install
        echo Attempting manual dependency installation...
        echo This is a workaround for npm issues...
        echo.
        echo Please manually run: npm install
        echo Or install yarn and run: yarn install
        echo.
        echo For now, starting with existing dependencies...
    ) else (
        echo ✅ Dependencies installed successfully
    )
) else (
    echo ✅ Dependencies already installed
)

echo.
echo 🌐 Starting Rizz Trader System...
echo.
echo 📊 Dashboard: http://localhost:3000
echo 📈 Legacy Chart: http://localhost:3000 (Chart tab)
echo 💰 Wallet: http://localhost:3000 (Wallet tab)
echo.
echo ⚠️  IMPORTANT:
echo   • System starts in TESTNET mode
echo   • Auto-trading is DISABLED by default
echo   • Configure wallets in Settings before trading
echo   • Always test on testnet first!
echo.

:: Open browser
echo Opening browser...
start "" "http://localhost:3000"

echo Starting development server...
echo Press Ctrl+C to stop the server
echo.

:: Use the working method you discovered
node node_modules/next/dist/bin/next dev

:: If we get here, the server stopped
echo.
echo 🛑 Rizz Trader System has stopped
echo.
pause
