import logger from './logger'

// Token mapping for CoinGecko API
const TOKEN_MAPPING: { [key: string]: string } = {
  SOL: "solana",
  PENGU: "pudgy-penguins",
  TOAD: "toad-network", // This might need to be updated with correct ID
  BONK: "bonk",
  WIF: "dogwifcoin",
  JUP: "jupiter-exchange-solana",
  PYTH: "pyth-network",
  JTO: "jito-governance-token",
  RNDR: "render-token",
  HNT: "helium",
  MOBILE: "helium-mobile"
}

export interface PricePoint {
  timestamp: number
  price: number
}

export interface TokenData {
  symbol: string
  name: string
  currentPrice: number
  priceChange24h: number
  priceChangePercentage24h: number
  marketCap: number
  volume24h: number
  prices: PricePoint[]
  lastUpdated: number
}

export interface MarketData {
  [symbol: string]: TokenData
}

class DataFetcher {
  private cache: Map<string, { data: TokenData; expiry: number }> = new Map()
  private readonly CACHE_DURATION = 60000 // 1 minute cache
  private readonly BASE_URL = 'https://api.coingecko.com/api/v3'

  private getCoinId(symbol: string): string {
    return TOKEN_MAPPING[symbol.toUpperCase()] || symbol.toLowerCase()
  }

  private async fetchWithRetry(url: string, retries: number = 3): Promise<Response> {
    for (let i = 0; i < retries; i++) {
      try {
        const response = await fetch(url)
        if (response.ok) {
          return response
        }
        if (response.status === 429) {
          // Rate limited, wait before retry
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
          continue
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      } catch (error) {
        if (i === retries - 1) throw error
        await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)))
      }
    }
    throw new Error('Max retries exceeded')
  }

  async fetchTokenData(symbol: string, days: number = 7): Promise<TokenData> {
    const cacheKey = `${symbol}_${days}`
    const cached = this.cache.get(cacheKey)
    
    if (cached && Date.now() < cached.expiry) {
      return cached.data
    }

    try {
      const coinId = this.getCoinId(symbol)
      
      // Fetch current price and market data
      const marketUrl = `${this.BASE_URL}/coins/${coinId}`
      const marketResponse = await this.fetchWithRetry(marketUrl)
      const marketData = await marketResponse.json()

      // Fetch historical price data
      const historyUrl = `${this.BASE_URL}/coins/${coinId}/market_chart?vs_currency=usd&days=${days}`
      const historyResponse = await this.fetchWithRetry(historyUrl)
      const historyData = await historyResponse.json()

      const tokenData: TokenData = {
        symbol: symbol.toUpperCase(),
        name: marketData.name || symbol,
        currentPrice: marketData.market_data?.current_price?.usd || 0,
        priceChange24h: marketData.market_data?.price_change_24h || 0,
        priceChangePercentage24h: marketData.market_data?.price_change_percentage_24h || 0,
        marketCap: marketData.market_data?.market_cap?.usd || 0,
        volume24h: marketData.market_data?.total_volume?.usd || 0,
        prices: historyData.prices?.map(([timestamp, price]: [number, number]) => ({
          timestamp,
          price
        })) || [],
        lastUpdated: Date.now()
      }

      // Cache the result
      this.cache.set(cacheKey, {
        data: tokenData,
        expiry: Date.now() + this.CACHE_DURATION
      })

      await logger.debug('Fetched token data', { symbol, coinId, currentPrice: tokenData.currentPrice })
      return tokenData

    } catch (error) {
      await logger.error('Failed to fetch token data', error, { symbol })
      throw new Error(`Failed to fetch data for ${symbol}: ${error}`)
    }
  }

  async fetchMultipleTokens(symbols: string[], days: number = 7): Promise<MarketData> {
    const results: MarketData = {}
    
    // Fetch tokens in parallel but with some delay to avoid rate limiting
    const promises = symbols.map((symbol, index) => 
      new Promise<void>(async (resolve) => {
        try {
          // Add small delay between requests to avoid rate limiting
          await new Promise(r => setTimeout(r, index * 200))
          results[symbol] = await this.fetchTokenData(symbol, days)
        } catch (error) {
          await logger.warn('Failed to fetch token in batch', { symbol, error })
          // Create empty data for failed tokens
          results[symbol] = {
            symbol: symbol.toUpperCase(),
            name: symbol,
            currentPrice: 0,
            priceChange24h: 0,
            priceChangePercentage24h: 0,
            marketCap: 0,
            volume24h: 0,
            prices: [],
            lastUpdated: Date.now()
          }
        }
        resolve()
      })
    )

    await Promise.all(promises)
    return results
  }

  async fetchRealTimePrice(symbol: string): Promise<number> {
    try {
      const coinId = this.getCoinId(symbol)
      const url = `${this.BASE_URL}/simple/price?ids=${coinId}&vs_currencies=usd`
      
      const response = await this.fetchWithRetry(url)
      const data = await response.json()
      
      const price = data[coinId]?.usd || 0
      await logger.debug('Fetched real-time price', { symbol, price })
      
      return price
    } catch (error) {
      await logger.error('Failed to fetch real-time price', error, { symbol })
      return 0
    }
  }

  async fetchMultiplePrices(symbols: string[]): Promise<{ [symbol: string]: number }> {
    try {
      const coinIds = symbols.map(symbol => this.getCoinId(symbol))
      const url = `${this.BASE_URL}/simple/price?ids=${coinIds.join(',')}&vs_currencies=usd`
      
      const response = await this.fetchWithRetry(url)
      const data = await response.json()
      
      const prices: { [symbol: string]: number } = {}
      symbols.forEach(symbol => {
        const coinId = this.getCoinId(symbol)
        prices[symbol] = data[coinId]?.usd || 0
      })
      
      await logger.debug('Fetched multiple prices', { symbols, prices })
      return prices
    } catch (error) {
      await logger.error('Failed to fetch multiple prices', error, { symbols })
      return symbols.reduce((acc, symbol) => ({ ...acc, [symbol]: 0 }), {})
    }
  }

  // Alternative data source for when CoinGecko is unavailable
  async fetchFromAlternativeSource(symbol: string): Promise<TokenData | null> {
    try {
      // This could be implemented with other free APIs like:
      // - CoinCap API
      // - CryptoCompare (free tier)
      // - Binance public API
      // For now, return null to indicate unavailable
      
      await logger.debug('Alternative data source not implemented', { symbol })
      return null
    } catch (error) {
      await logger.error('Alternative data source failed', error, { symbol })
      return null
    }
  }

  clearCache(): void {
    this.cache.clear()
    logger.debug('Data cache cleared')
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  // Get supported tokens
  getSupportedTokens(): string[] {
    return Object.keys(TOKEN_MAPPING)
  }

  // Check if token is supported
  isTokenSupported(symbol: string): boolean {
    return symbol.toUpperCase() in TOKEN_MAPPING
  }

  // Add custom token mapping
  addTokenMapping(symbol: string, coinGeckoId: string): void {
    TOKEN_MAPPING[symbol.toUpperCase()] = coinGeckoId
    logger.info('Added custom token mapping', { symbol, coinGeckoId })
  }
}

export const dataFetcher = new DataFetcher()
export default dataFetcher
