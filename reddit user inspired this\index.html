<script type="text/javascript">
        var gk_isXlsx = false;
        var gk_xlsxFileLookup = {};
        var gk_fileData = {};
        function filledCell(cell) {
          return cell !== '' && cell != null;
        }
        function loadFileData(filename) {
        if (gk_isXlsx && gk_xlsxFileLookup[filename]) {
            try {
                var workbook = XLSX.read(gk_fileData[filename], { type: 'base64' });
                var firstSheetName = workbook.SheetNames[0];
                var worksheet = workbook.Sheets[firstSheetName];

                // Convert sheet to JSON to filter blank rows
                var jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1, blankrows: false, defval: '' });
                // Filter out blank rows (rows where all cells are empty, null, or undefined)
                var filteredData = jsonData.filter(row => row.some(filledCell));

                // Heuristic to find the header row by ignoring rows with fewer filled cells than the next row
                var headerRowIndex = filteredData.findIndex((row, index) =>
                  row.filter(filledCell).length >= filteredData[index + 1]?.filter(filledCell).length
                );
                // Fallback
                if (headerRowIndex === -1 || headerRowIndex > 25) {
                  headerRowIndex = 0;
                }

                // Convert filtered JSON back to CSV
                var csv = XLSX.utils.aoa_to_sheet(filteredData.slice(headerRowIndex)); // Create a new sheet from filtered array of arrays
                csv = XLSX.utils.sheet_to_csv(csv, { header: 1 });
                return csv;
            } catch (e) {
                console.error(e);
                return "";
            }
        }
        return gk_fileData[filename] || "";
        }
        </script><!DOCTYPE html>
<html>
<head>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation@2.2.1/dist/chartjs-plugin-annotation.min.js"></script>
  <style>
    canvas { max-width: 100%; }
    #blurb { display: none; position: absolute; background: white; padding: 5px; border: 1px solid black; }
  </style>
</head>
<body>
  <canvas id="chart"></canvas>
  <div id="blurb"></div>
  <script>
    fetch('https://api.coingecko.com/api/v3/coins/penguin-finance/market_chart?vs_currency=usd&days=90')
      .then(response => response.json())
      .then(data => {
        const prices = data.prices.map(p => p[1]);
        const times = data.prices.map(p => new Date(p[0]).toLocaleDateString());
        const midIndex = Math.floor(prices.length * 0.5);
        const boxCenter = prices[midIndex];
        const boxRange = boxCenter * 0.1;
        const ema50 = prices.reduce((sum, val, i, arr) => sum + val / Math.min(50, arr.length - i), 0) / 50;
        const currentPrice = prices[prices.length - 1];

        const ctx = document.getElementById('chart').getContext('2d');
        const chart = new Chart(ctx, {
          type: 'line',
          data: {
            labels: times,
            datasets: [{
              label: 'PENGU Price (USD)',
              data: prices,
              borderColor: 'rgba(75, 192, 192, 1)',
              fill: false
            }, {
              label: '50 EMA',
              data: prices.map((_, i) => {
                const slice = prices.slice(Math.max(0, i - 49), i + 1);
                return slice.reduce((sum, val) => sum + val / slice.length, 0);
              }),
              borderColor: 'rgba(0, 255, 0, 0.5)',
              fill: false,
              borderDash: [5, 5]
            }]
          },
          options: {
            scales: { y: { beginAtZero: false } },
            plugins: {
              tooltip: {
                enabled: true,
                callbacks: {
                  label: function(context) {
                    const annotation = chart.options.plugins.annotation.annotations[context.datasetIndex];
                    if (annotation) {
                      return annotation.label ? annotation.label.content : '';
                    }
                  }
                }
              },
              annotation: {
                annotations: [{
                  type: 'box',
                  xMin: times.length * 0.4,
                  xMax: times.length * 0.6,
                  yMin: boxCenter - boxRange,
                  yMax: boxCenter + boxRange,
                  backgroundColor: 'rgba(255, 165, 0, 0.3)',
                  borderColor: 'orange',
                  borderWidth: 2,
                  label: { content: 'Key Support/Resistance Zone', enabled: true, position: 'center' },
                  onClick: () => showBlurb(`Entry: Buy if breaks ${boxCenter + boxRange.toFixed(2)}. Exit: Sell if drops below ${boxCenter - boxRange.toFixed(2)}.`)
                }, {
                  type: 'line',
                  xMin: times.length * 0.65,
                  xMax: times.length * 0.65,
                  yMin: boxCenter + boxRange * 0.5,
                  yMax: currentPrice,
                  borderColor: 'blue',
                  borderWidth: 2,
                  label: { content: 'Entry Point', enabled: true, position: 'start' },
                  onClick: () => showBlurb(`Entry: Buy at ${boxCenter + boxRange * 0.5}. Exit: Sell at ${currentPrice * 1.1}.`)
                }, {
                  type: 'box',
                  xMin: 0,
                  xMax: times.length,
                  yMin: ema50 * 0.95,
                  yMax: ema50 * 1.05,
                  backgroundColor: 'rgba(255, 0, 0, 0.3)',
                  borderColor: 'red',
                  borderWidth: 2,
                  label: { content: 'Rejection Zone', enabled: true, position: 'center' },
                  onClick: () => showBlurb(`Exit: Sell if rejects below ${ema50}. Entry: Buy if bounces to ${ema50 * 1.05}.`)
                }, {
                  type: 'line',
                  xMin: times.length * 0.45,
                  xMax: times.length * 0.6,
                  yMin: boxCenter - boxRange,
                  yMax: boxCenter + boxRange * 1.5,
                  borderColor: 'green',
                  borderWidth: 2,
                  label: { content: 'Breakout Vector', enabled: true, position: 'start' },
                  onClick: () => showBlurb(`Entry: Buy if breaks ${boxCenter + boxRange}. Exit: Sell at ${boxCenter + boxRange * 1.5}.`)
                }]
              }
            }
          }
        });

        function showBlurb(text) {
          const blurb = document.getElementById('blurb');
          blurb.style.left = event.pageX + 'px';
          blurb.style.top = event.pageY + 'px';
          blurb.textContent = text;
          blurb.style.display = 'block';
          setTimeout(() => blurb.style.display = 'none', 3000);
        }
      });
  </script>
</body>
</html>