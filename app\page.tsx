"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Settings } from 'lucide-react'
import Dashboard from '../components/dashboard'
import LegacyChart from '../components/legacy-chart'
import WalletTransactions from '../components/wallet-transactions'
import ConfigModal from '../components/config-modal'

export default function Home() {
  const [activeTab, setActiveTab] = useState("dashboard")

  return (
    <main className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold text-slate-800 mb-2">
              🚀 Rizz Trader System
            </h1>
            <p className="text-slate-600 text-lg">
              Automated Solana Trading with Legacy Chart Analysis
            </p>
          </div>
          <ConfigModal>
            <Button variant="outline" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Settings
            </Button>
          </ConfigModal>
        </div>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-3 h-12">
            <TabsTrigger value="dashboard" className="text-lg">
              📊 Dashboard
            </TabsTrigger>
            <TabsTrigger value="chart" className="text-lg">
              📈 Legacy Chart
            </TabsTrigger>
            <TabsTrigger value="wallet" className="text-lg">
              💰 Wallet
            </TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-4">
            <Dashboard />
          </TabsContent>

          <TabsContent value="chart" className="space-y-4">
            <LegacyChart />
          </TabsContent>

          <TabsContent value="wallet" className="space-y-4">
            <WalletTransactions />
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )

}
