// Client-side logger using localStorage and console

export interface TradeLog {
  id: string
  timestamp: string
  token: string
  type: 'BUY' | 'SELL'
  amount: number
  price: number
  usdValue: number
  txHash: string
  gasUsed: number
  gasCost: number
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
  wallet: string
  signal?: {
    type: string
    probability: number
    indicators: Record<string, any>
  }
}

export interface PositionLog {
  id: string
  token: string
  entryTimestamp: string
  exitTimestamp?: string
  entryPrice: number
  exitPrice?: number
  amount: number
  usdValue: number
  status: 'OPEN' | 'CLOSED' | 'FAILED'
  pnl?: number
  pnlPercent?: number
  wallet: string
}

export interface DebugLog {
  id: string
  timestamp: string
  level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG'
  message: string
  data?: any
  error?: string
  stack?: string
}

class Logger {
  private readonly TRADES_KEY = 'rizz_trader_trades'
  private readonly POSITIONS_KEY = 'rizz_trader_positions'
  private readonly DEBUG_KEY = 'rizz_trader_debug'
  private readonly MAX_LOGS = 1000 // Keep only last 1000 logs

  constructor() {
    // Initialize storage if needed
    this.initializeStorage()
  }

  private initializeStorage() {
    if (typeof window === 'undefined') return

    if (!localStorage.getItem(this.TRADES_KEY)) {
      localStorage.setItem(this.TRADES_KEY, JSON.stringify([]))
    }
    if (!localStorage.getItem(this.POSITIONS_KEY)) {
      localStorage.setItem(this.POSITIONS_KEY, JSON.stringify([]))
    }
    if (!localStorage.getItem(this.DEBUG_KEY)) {
      localStorage.setItem(this.DEBUG_KEY, JSON.stringify([]))
    }
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substring(2)
  }

  private trimLogs(key: string) {
    if (typeof window === 'undefined') return

    try {
      const logs = JSON.parse(localStorage.getItem(key) || '[]')
      if (logs.length > this.MAX_LOGS) {
        const trimmed = logs.slice(-this.MAX_LOGS)
        localStorage.setItem(key, JSON.stringify(trimmed))
      }
    } catch (error) {
      console.error('Failed to trim logs:', error)
    }
  }

  async logTrade(trade: Omit<TradeLog, 'id' | 'timestamp'>): Promise<void> {
    if (typeof window === 'undefined') return

    try {
      const tradeLog: TradeLog = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        ...trade
      }

      const trades = JSON.parse(localStorage.getItem(this.TRADES_KEY) || '[]')
      trades.push(tradeLog)
      localStorage.setItem(this.TRADES_KEY, JSON.stringify(trades))
      
      this.trimLogs(this.TRADES_KEY)
      
      console.log('Trade logged:', tradeLog)
    } catch (error) {
      console.error('Failed to log trade:', error)
    }
  }

  async logPosition(position: Omit<PositionLog, 'id' | 'entryTimestamp'>): Promise<void> {
    if (typeof window === 'undefined') return

    try {
      const positionLog: PositionLog = {
        id: this.generateId(),
        entryTimestamp: new Date().toISOString(),
        ...position
      }

      const positions = JSON.parse(localStorage.getItem(this.POSITIONS_KEY) || '[]')
      positions.push(positionLog)
      localStorage.setItem(this.POSITIONS_KEY, JSON.stringify(positions))
      
      this.trimLogs(this.POSITIONS_KEY)
      
      console.log('Position logged:', positionLog)
    } catch (error) {
      console.error('Failed to log position:', error)
    }
  }

  async updatePosition(positionId: string, updates: Partial<PositionLog>): Promise<void> {
    if (typeof window === 'undefined') return

    try {
      const positions = JSON.parse(localStorage.getItem(this.POSITIONS_KEY) || '[]')
      const index = positions.findIndex((p: PositionLog) => p.id === positionId)
      
      if (index !== -1) {
        positions[index] = { ...positions[index], ...updates }
        if (updates.status === 'CLOSED' && !updates.exitTimestamp) {
          positions[index].exitTimestamp = new Date().toISOString()
        }
        localStorage.setItem(this.POSITIONS_KEY, JSON.stringify(positions))
        console.log('Position updated:', positions[index])
      }
    } catch (error) {
      console.error('Failed to update position:', error)
    }
  }

  async info(message: string, data?: any): Promise<void> {
    await this.log('INFO', message, data)
  }

  async warn(message: string, data?: any): Promise<void> {
    await this.log('WARN', message, data)
  }

  async error(message: string, error?: any, data?: any): Promise<void> {
    const errorData = {
      ...data,
      error: error instanceof Error ? error.message : error,
      stack: error instanceof Error ? error.stack : undefined
    }
    await this.log('ERROR', message, errorData)
  }

  async debug(message: string, data?: any): Promise<void> {
    await this.log('DEBUG', message, data)
  }

  private async log(level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG', message: string, data?: any): Promise<void> {
    if (typeof window === 'undefined') {
      // Server-side: just use console
      console.log(`[${level}] ${message}`, data)
      return
    }

    try {
      const debugLog: DebugLog = {
        id: this.generateId(),
        timestamp: new Date().toISOString(),
        level,
        message,
        data,
        error: data?.error,
        stack: data?.stack
      }

      const logs = JSON.parse(localStorage.getItem(this.DEBUG_KEY) || '[]')
      logs.push(debugLog)
      localStorage.setItem(this.DEBUG_KEY, JSON.stringify(logs))
      
      this.trimLogs(this.DEBUG_KEY)

      // Also log to console
      const consoleMethod = level === 'ERROR' ? 'error' : level === 'WARN' ? 'warn' : 'log'
      console[consoleMethod](`[${level}] ${message}`, data)
    } catch (error) {
      console.error('Failed to log debug message:', error)
    }
  }

  async getTrades(limit?: number): Promise<TradeLog[]> {
    if (typeof window === 'undefined') return []

    try {
      const trades = JSON.parse(localStorage.getItem(this.TRADES_KEY) || '[]')
      return limit ? trades.slice(-limit) : trades
    } catch (error) {
      console.error('Failed to get trades:', error)
      return []
    }
  }

  async getPositions(limit?: number): Promise<PositionLog[]> {
    if (typeof window === 'undefined') return []

    try {
      const positions = JSON.parse(localStorage.getItem(this.POSITIONS_KEY) || '[]')
      return limit ? positions.slice(-limit) : positions
    } catch (error) {
      console.error('Failed to get positions:', error)
      return []
    }
  }

  async getDebugLogs(limit?: number): Promise<DebugLog[]> {
    if (typeof window === 'undefined') return []

    try {
      const logs = JSON.parse(localStorage.getItem(this.DEBUG_KEY) || '[]')
      return limit ? logs.slice(-limit) : logs
    } catch (error) {
      console.error('Failed to get debug logs:', error)
      return []
    }
  }

  async clearLogs(): Promise<void> {
    if (typeof window === 'undefined') return

    try {
      localStorage.setItem(this.TRADES_KEY, JSON.stringify([]))
      localStorage.setItem(this.POSITIONS_KEY, JSON.stringify([]))
      localStorage.setItem(this.DEBUG_KEY, JSON.stringify([]))
      console.log('All logs cleared')
    } catch (error) {
      console.error('Failed to clear logs:', error)
    }
  }
}

const logger = new Logger()
export default logger
