@echo off
echo ========================================
echo    RIZZ TRADER - NPM FIX STARTUP
echo ========================================
echo.

echo Node.js version detected:
node --version
echo.

echo Attempting to fix npm issue...
echo.

:: Try different npm approaches
echo Method 1: Testing npm directly...
npm --version 2>nul
if %errorlevel% equ 0 (
    echo npm is working!
    goto :npm_works
)

echo Method 1 failed. Trying Method 2: Using npx...
npx --version 2>nul
if %errorlevel% equ 0 (
    echo npx is working! Using npx instead of npm.
    goto :use_npx
)

echo Method 2 failed. Trying Method 3: Reinstalling npm...
echo.
echo Downloading and installing npm...
node -e "console.log('Node.js is working')"
if %errorlevel% neq 0 (
    echo ERROR: Node.js itself is not working properly
    goto :error_exit
)

:: Try to install npm using Node.js
echo Installing npm globally...
node -e "const https=require('https');const fs=require('fs');https.get('https://registry.npmjs.org/npm/latest',(r)=>{let d='';r.on('data',c=>d+=c);r.on('end',()=>console.log(JSON.parse(d).version))})"

echo.
echo If npm is still not working, we'll use alternative methods...
goto :alternative_start

:npm_works
echo Using npm normally...
echo.
goto :start_with_npm

:use_npx
echo Using npx instead of npm...
echo.
goto :start_with_npx

:start_with_npm
echo Checking for package.json...
if not exist "package.json" (
    echo ERROR: package.json not found in %CD%
    goto :error_exit
)

echo Installing dependencies with npm...
npm install
if %errorlevel% neq 0 (
    echo npm install failed, trying alternative...
    goto :alternative_start
)

echo Starting with npm...
start "" "http://localhost:3000"
npm run dev
goto :end

:start_with_npx
echo Checking for package.json...
if not exist "package.json" (
    echo ERROR: package.json not found in %CD%
    goto :error_exit
)

echo Installing dependencies with npx...
npx npm install
if %errorlevel% neq 0 (
    echo npx install failed, trying alternative...
    goto :alternative_start
)

echo Starting with npx...
start "" "http://localhost:3000"
npx next dev
goto :end

:alternative_start
echo ========================================
echo Using alternative startup method...
echo ========================================
echo.

echo Checking for package.json...
if not exist "package.json" (
    echo ERROR: package.json not found in %CD%
    echo Please make sure you're in the correct directory
    goto :error_exit
)

echo Trying to start Next.js directly...
echo Opening browser...
start "" "http://localhost:3000"

echo Starting development server...
node_modules\.bin\next dev
if %errorlevel% neq 0 (
    echo Direct Next.js start failed
    echo.
    echo MANUAL STEPS REQUIRED:
    echo 1. Open Command Prompt as Administrator
    echo 2. Run: npm install -g npm@latest
    echo 3. Restart your computer
    echo 4. Try running this script again
    echo.
    goto :error_exit
)

goto :end

:error_exit
echo.
echo ========================================
echo TROUBLESHOOTING STEPS:
echo ========================================
echo.
echo 1. Open Command Prompt as Administrator
echo 2. Run: npm install -g npm@latest
echo 3. If that fails, reinstall Node.js from https://nodejs.org/
echo 4. Make sure to check "Add to PATH" during installation
echo 5. Restart your computer
echo 6. Try this script again
echo.
echo Alternative: Use yarn instead of npm:
echo 1. Install yarn: npm install -g yarn
echo 2. Run: yarn install
echo 3. Run: yarn dev
echo.
pause
exit /b 1

:end
echo.
echo Server stopped.
pause
