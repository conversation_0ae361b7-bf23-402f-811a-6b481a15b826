import { TokenData, PricePoint } from './data-fetcher'
import configManager from './config-manager'
import logger from './logger'

export interface TechnicalIndicators {
  ema50: number[]
  supportLevel: number
  resistanceLevel: number
  currentTrend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS'
  volume: number[]
  rsi: number
  macd: {
    line: number
    signal: number
    histogram: number
  }
}

export interface TradingSignal {
  type: 'BUY' | 'SELL' | 'HOLD'
  strength: 'WEAK' | 'MODERATE' | 'STRONG'
  probability: number
  confidence: number
  price: number
  timestamp: number
  indicators: TechnicalIndicators
  reasoning: string[]
  targetPrice?: number
  stopLoss?: number
  riskReward?: number
}

export interface SignalAnalysis {
  symbol: string
  currentSignal: TradingSignal
  previousSignals: TradingSignal[]
  marketCondition: 'TRENDING' | 'RANGING' | 'VOLATILE'
  lastAnalyzed: number
}

class SignalAnalyzer {
  private signalHistory: Map<string, TradingSignal[]> = new Map()

  // Calculate Exponential Moving Average
  private calculateEMA(prices: number[], period: number): number[] {
    if (prices.length < period) return []
    
    const ema: number[] = []
    const multiplier = 2 / (period + 1)
    
    // Start with SMA for first value
    let sum = 0
    for (let i = 0; i < period; i++) {
      sum += prices[i]
    }
    ema[period - 1] = sum / period
    
    // Calculate EMA for remaining values
    for (let i = period; i < prices.length; i++) {
      ema[i] = (prices[i] * multiplier) + (ema[i - 1] * (1 - multiplier))
    }
    
    return ema
  }

  // Calculate RSI (Relative Strength Index)
  private calculateRSI(prices: number[], period: number = 14): number {
    if (prices.length < period + 1) return 50
    
    let gains = 0
    let losses = 0
    
    // Calculate initial average gain and loss
    for (let i = 1; i <= period; i++) {
      const change = prices[i] - prices[i - 1]
      if (change > 0) gains += change
      else losses -= change
    }
    
    let avgGain = gains / period
    let avgLoss = losses / period
    
    // Calculate RSI for remaining periods
    for (let i = period + 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1]
      const gain = change > 0 ? change : 0
      const loss = change < 0 ? -change : 0
      
      avgGain = (avgGain * (period - 1) + gain) / period
      avgLoss = (avgLoss * (period - 1) + loss) / period
    }
    
    if (avgLoss === 0) return 100
    const rs = avgGain / avgLoss
    return 100 - (100 / (1 + rs))
  }

  // Calculate MACD
  private calculateMACD(prices: number[]): { line: number; signal: number; histogram: number } {
    const ema12 = this.calculateEMA(prices, 12)
    const ema26 = this.calculateEMA(prices, 26)
    
    if (ema12.length === 0 || ema26.length === 0) {
      return { line: 0, signal: 0, histogram: 0 }
    }
    
    const macdLine = ema12[ema12.length - 1] - ema26[ema26.length - 1]
    
    // For simplicity, using a basic signal line calculation
    const signalLine = macdLine * 0.9 // Simplified
    const histogram = macdLine - signalLine
    
    return { line: macdLine, signal: signalLine, histogram }
  }

  // Identify support and resistance levels using the legacy "orange box" methodology
  private identifySupportResistance(prices: number[]): { support: number; resistance: number } {
    if (prices.length === 0) return { support: 0, resistance: 0 }
    
    const config = configManager.getConfig()
    const range = config?.signals.supportResistanceRange || 0.1
    
    // Use middle section of price data for support/resistance calculation (legacy approach)
    const midIndex = Math.floor(prices.length * 0.5)
    const startIndex = Math.floor(prices.length * 0.4)
    const endIndex = Math.floor(prices.length * 0.6)
    
    const relevantPrices = prices.slice(startIndex, endIndex)
    const avgPrice = relevantPrices.reduce((sum, price) => sum + price, 0) / relevantPrices.length
    
    const support = avgPrice * (1 - range)
    const resistance = avgPrice * (1 + range)
    
    return { support, resistance }
  }

  // Analyze market trend
  private analyzeTrend(prices: number[], ema: number[]): 'BULLISH' | 'BEARISH' | 'SIDEWAYS' {
    if (prices.length < 10 || ema.length < 10) return 'SIDEWAYS'
    
    const currentPrice = prices[prices.length - 1]
    const currentEMA = ema[ema.length - 1]
    const previousEMA = ema[ema.length - 10]
    
    const priceAboveEMA = currentPrice > currentEMA
    const emaRising = currentEMA > previousEMA
    
    if (priceAboveEMA && emaRising) return 'BULLISH'
    if (!priceAboveEMA && !emaRising) return 'BEARISH'
    return 'SIDEWAYS'
  }

  // Calculate signal probability based on multiple factors
  private calculateProbability(indicators: TechnicalIndicators, currentPrice: number): number {
    let probability = 0.5 // Base probability
    
    const { supportLevel, resistanceLevel, currentTrend, rsi, macd } = indicators
    
    // Support/Resistance factor
    if (currentPrice <= supportLevel * 1.02) probability += 0.15 // Near support
    if (currentPrice >= resistanceLevel * 0.98) probability -= 0.15 // Near resistance
    
    // Trend factor
    if (currentTrend === 'BULLISH') probability += 0.1
    if (currentTrend === 'BEARISH') probability -= 0.1
    
    // RSI factor
    if (rsi < 30) probability += 0.1 // Oversold
    if (rsi > 70) probability -= 0.1 // Overbought
    
    // MACD factor
    if (macd.histogram > 0 && macd.line > macd.signal) probability += 0.05
    if (macd.histogram < 0 && macd.line < macd.signal) probability -= 0.05
    
    // Ensure probability is between 0 and 1
    return Math.max(0, Math.min(1, probability))
  }

  // Generate trading signal based on technical analysis
  async analyzeToken(tokenData: TokenData): Promise<TradingSignal> {
    try {
      const prices = tokenData.prices.map(p => p.price)
      const currentPrice = tokenData.currentPrice
      
      if (prices.length < 50) {
        throw new Error('Insufficient price data for analysis')
      }

      // Calculate technical indicators
      const ema50 = this.calculateEMA(prices, 50)
      const { support, resistance } = this.identifySupportResistance(prices)
      const trend = this.analyzeTrend(prices, ema50)
      const rsi = this.calculateRSI(prices)
      const macd = this.calculateMACD(prices)
      
      const indicators: TechnicalIndicators = {
        ema50,
        supportLevel: support,
        resistanceLevel: resistance,
        currentTrend: trend,
        volume: [], // Would be populated with actual volume data
        rsi,
        macd
      }

      // Determine signal type and strength
      let signalType: 'BUY' | 'SELL' | 'HOLD' = 'HOLD'
      let strength: 'WEAK' | 'MODERATE' | 'STRONG' = 'WEAK'
      const reasoning: string[] = []

      // Legacy "orange box" breakout logic
      if (currentPrice > resistance && trend === 'BULLISH') {
        signalType = 'BUY'
        strength = 'STRONG'
        reasoning.push('Price broke above resistance level')
        reasoning.push('Bullish trend confirmed')
      } else if (currentPrice < support && trend === 'BEARISH') {
        signalType = 'SELL'
        strength = 'STRONG'
        reasoning.push('Price broke below support level')
        reasoning.push('Bearish trend confirmed')
      } else if (currentPrice <= support * 1.02 && rsi < 40) {
        signalType = 'BUY'
        strength = 'MODERATE'
        reasoning.push('Price near support with oversold RSI')
      } else if (currentPrice >= resistance * 0.98 && rsi > 60) {
        signalType = 'SELL'
        strength = 'MODERATE'
        reasoning.push('Price near resistance with overbought RSI')
      }

      // Calculate probability
      const probability = this.calculateProbability(indicators, currentPrice)
      
      // Only generate strong signals if probability > 90%
      const config = configManager.getConfig()
      const minProbability = config?.signals.minProbability || 0.9
      
      if (probability < minProbability && signalType !== 'HOLD') {
        signalType = 'HOLD'
        reasoning.push(`Probability ${(probability * 100).toFixed(1)}% below threshold ${(minProbability * 100)}%`)
      }

      // Calculate target price and stop loss
      let targetPrice: number | undefined
      let stopLoss: number | undefined
      let riskReward: number | undefined

      if (signalType === 'BUY') {
        targetPrice = resistance * 1.1 // 10% above resistance
        stopLoss = support * 0.95 // 5% below support
        riskReward = (targetPrice - currentPrice) / (currentPrice - stopLoss)
      } else if (signalType === 'SELL') {
        targetPrice = support * 0.9 // 10% below support
        stopLoss = resistance * 1.05 // 5% above resistance
        riskReward = (currentPrice - targetPrice) / (stopLoss - currentPrice)
      }

      const signal: TradingSignal = {
        type: signalType,
        strength,
        probability,
        confidence: probability * 100,
        price: currentPrice,
        timestamp: Date.now(),
        indicators,
        reasoning,
        targetPrice,
        stopLoss,
        riskReward
      }

      // Store signal in history
      const history = this.signalHistory.get(tokenData.symbol) || []
      history.push(signal)
      if (history.length > 100) history.shift() // Keep last 100 signals
      this.signalHistory.set(tokenData.symbol, history)

      await logger.debug('Generated trading signal', { 
        symbol: tokenData.symbol, 
        signal: signalType, 
        probability: probability * 100,
        strength 
      })

      return signal

    } catch (error) {
      await logger.error('Failed to analyze token', error, { symbol: tokenData.symbol })
      throw error
    }
  }

  // Analyze multiple tokens
  async analyzeMultipleTokens(tokensData: { [symbol: string]: TokenData }): Promise<{ [symbol: string]: SignalAnalysis }> {
    const results: { [symbol: string]: SignalAnalysis } = {}
    
    for (const [symbol, tokenData] of Object.entries(tokensData)) {
      try {
        const signal = await this.analyzeToken(tokenData)
        const history = this.signalHistory.get(symbol) || []
        
        results[symbol] = {
          symbol,
          currentSignal: signal,
          previousSignals: history.slice(-10), // Last 10 signals
          marketCondition: this.determineMarketCondition(tokenData),
          lastAnalyzed: Date.now()
        }
      } catch (error) {
        await logger.warn('Failed to analyze token in batch', { symbol, error })
      }
    }
    
    return results
  }

  private determineMarketCondition(tokenData: TokenData): 'TRENDING' | 'RANGING' | 'VOLATILE' {
    const prices = tokenData.prices.map(p => p.price)
    if (prices.length < 20) return 'RANGING'
    
    // Calculate price volatility
    const returns = []
    for (let i = 1; i < prices.length; i++) {
      returns.push((prices[i] - prices[i - 1]) / prices[i - 1])
    }
    
    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length
    const volatility = Math.sqrt(variance)
    
    if (volatility > 0.05) return 'VOLATILE'
    if (Math.abs(avgReturn) > 0.01) return 'TRENDING'
    return 'RANGING'
  }

  getSignalHistory(symbol: string): TradingSignal[] {
    return this.signalHistory.get(symbol) || []
  }

  clearSignalHistory(symbol?: string): void {
    if (symbol) {
      this.signalHistory.delete(symbol)
    } else {
      this.signalHistory.clear()
    }
  }
}

export const signalAnalyzer = new SignalAnalyzer()
export default signalAnalyzer
