# 🚀 Rizz Trader System - Automated Solana Trading

An advanced automated trading system built on top of the legacy manual trading methodology, featuring real-time signal analysis, automated trade execution, and comprehensive portfolio management.

## 🌟 Features

### 📊 **Dashboard Tab**
- **Real-time Portfolio Overview**: Live tracking of open/closed positions with P&L calculations
- **Token Cards**: Large cards for SOL, TOAD, and PENGU with current signals and probabilities
- **Position Management**: Detailed view of all trading positions with entry/exit data
- **Automated Trading Controls**: Start/stop automated trading with safety controls

### 📈 **Legacy Chart Tab**
- **Enhanced Chart Analysis**: All original "orange box" methodology with automation overlays
- **Interactive Indicators**: Hover over support/resistance zones, entry points, and breakout vectors
- **Research Notes**: Built-in note-taking system for manual research and analysis
- **Real-time Data**: Live price feeds from CoinGecko API (no API key required)

### 💰 **Wallet Tab**
- **Multi-Wallet Support**: Manage both testnet and mainnet wallets
- **Balance Tracking**: Real-time SOL and token balance monitoring
- **Transaction History**: Complete transaction log with signatures and explorer links
- **Wallet Security**: Testnet private keys stored locally, mainnet public keys only

### ⚙️ **Configuration System**
- **Network Switching**: Easy testnet/mainnet mode switching with safety warnings
- **Trading Parameters**: Configurable stop-loss, take-profit, and position sizing
- **Signal Tuning**: Adjust RSI, EMA, and probability thresholds
- **Wallet Management**: Add, remove, and select active wallets

## 🏗️ System Architecture

### **Core Components**

1. **Configuration Manager** (`lib/config-manager.ts`)
   - Centralized configuration management
   - Wallet CRUD operations
   - Network mode switching

2. **Signal Analyzer** (`lib/signal-analyzer.ts`)
   - Automated "orange box" methodology implementation
   - Technical indicator calculations (EMA, RSI, MACD)
   - High-probability signal generation (>90% accuracy target)

3. **Trading Engine** (`lib/trading-engine.ts`)
   - Automated trade execution based on signals
   - Gas fee vs profit analysis
   - Position management and stop-loss/take-profit

4. **Data Fetcher** (`lib/data-fetcher.ts`)
   - Free CoinGecko API integration
   - Real-time price data with caching
   - Support for major Solana tokens

5. **Wallet Manager** (`lib/wallet-manager.ts`)
   - Solana wallet integration
   - Balance checking and transaction history
   - Testnet/mainnet wallet support

6. **Logger** (`lib/logger.ts`)
   - Comprehensive JSON-based logging
   - Trade, position, and debug logs
   - Profit/loss tracking

## 🚀 Quick Start

### **1. Installation**
```bash
# Run the automated startup script
startup.bat
```

The startup script will:
- Check Node.js and npm installation
- Install all dependencies
- Create necessary directories and config files
- Build the application
- Launch the system at http://localhost:3000

### **2. Initial Configuration**
1. Open the application at http://localhost:3000
2. Click the "Settings" button in the top-right
3. Configure your first wallet:
   - **Testnet**: Add nickname, public key, and private key
   - **Mainnet**: Add nickname and public key only
4. Set trading parameters (start with small amounts!)
5. Enable desired tokens for monitoring

### **3. Safety First**
- ⚠️ **Always start in TESTNET mode**
- ⚠️ **Auto-trading is DISABLED by default**
- ⚠️ **Test thoroughly before using real funds**
- ⚠️ **Start with small position sizes**

## 📋 Trading Methodology

### **The "Orange Box" Strategy (Automated)**
The system automates the proven manual trading methodology:

1. **Support/Resistance Identification**: Automatically identifies key price levels
2. **Entry Point Calculation**: Determines optimal entry after retest confirmation
3. **Breakout Vector Analysis**: Projects breakout direction and targets
4. **Risk Management**: Implements stop-loss and take-profit based on EMA levels

### **Signal Generation Process**
1. **Data Collection**: Fetches 90-day price history for analysis
2. **Technical Analysis**: Calculates EMA, RSI, MACD, and support/resistance levels
3. **Pattern Recognition**: Identifies "orange box" setups and breakout patterns
4. **Probability Calculation**: Assigns confidence scores to each signal
5. **Trade Execution**: Executes trades only for signals above threshold (default 85%)

## 🔧 Configuration Options

### **Trading Parameters**
- `maxUsdPerTrade`: Maximum USD amount per trade (default: $5.00)
- `stopLossPercentage`: Stop-loss percentage (default: 5%)
- `takeProfitPercentage`: Take-profit percentage (default: 10%)
- `maxOpenPositions`: Maximum concurrent positions (default: 3)
- `tradingInterval`: Signal check interval in seconds (default: 30)

### **Signal Configuration**
- `minProbability`: Minimum signal probability (default: 0.85)
- `enabledTokens`: Tokens to monitor (default: SOL, PENGU, TOAD)
- `rsiOverbought`: RSI overbought level (default: 70)
- `rsiOversold`: RSI oversold level (default: 30)
- `emaShort`: Short EMA period (default: 12)
- `emaLong`: Long EMA period (default: 26)

## 📁 File Structure

```
rizz-trader-system/
├── app/
│   └── page.tsx                 # Main application layout
├── components/
│   ├── dashboard.tsx            # Portfolio dashboard
│   ├── legacy-chart.tsx         # Enhanced chart analysis
│   ├── wallet-transactions.tsx  # Wallet management
│   └── config-modal.tsx         # Configuration interface
├── lib/
│   ├── config-manager.ts        # Configuration management
│   ├── signal-analyzer.ts       # Trading signal analysis
│   ├── trading-engine.ts        # Automated trading logic
│   ├── data-fetcher.ts          # Price data fetching
│   ├── wallet-manager.ts        # Wallet operations
│   └── logger.ts                # Logging system
├── config/
│   ├── default-config.json      # Default configuration
│   └── config.json              # User configuration
├── logs/                        # Application logs
├── data/                        # Trading data storage
├── startup.bat                  # Automated startup script
└── README-AUTOMATED-SYSTEM.md   # This file
```

## 🔒 Security Considerations

### **Wallet Security**
- **Testnet**: Private keys stored locally for testing convenience
- **Mainnet**: Only public keys stored, transactions require external signing
- **Network Isolation**: Clear separation between testnet and mainnet operations

### **Trading Safety**
- **Default Disabled**: Auto-trading disabled by default
- **Position Limits**: Configurable maximum position sizes and counts
- **Gas Fee Protection**: Trades rejected if gas costs exceed potential profit
- **Stop-Loss Protection**: Automatic position closure on adverse price movements

## 📊 Monitoring and Logging

### **Log Files** (in `logs/` directory)
- `trades.json`: All executed trades with full details
- `positions.json`: Position tracking and P&L calculations
- `debug.json`: System debug information and errors

### **Real-time Monitoring**
- Dashboard shows live portfolio performance
- Position cards update with current prices and unrealized P&L
- Trading activity logged with timestamps and transaction hashes

## 🛠️ Troubleshooting

### **Common Issues**
1. **"Node.js not found"**: Install Node.js from https://nodejs.org/
2. **"Build failed"**: Run `npm install` to ensure all dependencies are installed
3. **"No price data"**: Check internet connection and CoinGecko API availability
4. **"Wallet connection failed"**: Verify wallet configuration and network settings

### **Debug Mode**
Enable debug logging in the configuration to get detailed system information.

## 🔄 Updates and Maintenance

### **Regular Tasks**
- Monitor log files for errors or unusual activity
- Review and adjust trading parameters based on performance
- Update token lists and configuration as needed
- Backup configuration and trading data regularly

### **Performance Optimization**
- The system is designed for 24/7 operation
- Automatic retry logic handles temporary API failures
- Efficient caching reduces API calls and improves response times

## ⚠️ Disclaimer

This automated trading system is for educational and research purposes. Cryptocurrency trading involves substantial risk of loss. Always:
- Test thoroughly on testnet before using real funds
- Start with small position sizes
- Monitor the system regularly
- Understand the risks involved
- Never invest more than you can afford to lose

## 📞 Support

For technical issues or questions about the system:
1. Check the log files in the `logs/` directory
2. Review the configuration settings
3. Ensure all dependencies are properly installed
4. Verify network connectivity and API availability

---

**Happy Trading! 🚀📈💰**
