// Client-side config management using localStorage

export interface WalletConfig {
  id: string
  nickname: string
  publicKey: string
  type: 'testnet' | 'mainnet'
  lastFour: string
  isActive: boolean
  secretKey?: string // Only for testnet wallets
}

export interface TradingConfig {
  maxPositions: number
  maxUsdPerTrade: number
  stopLossPercent: number
  takeProfitPercent: number
  riskManagement: {
    maxDailyLoss: number
    maxDrawdown: number
  }
}

export interface NotificationConfig {
  trades: boolean
  signals: boolean
  errors: boolean
}

export interface UIConfig {
  theme: 'light' | 'dark'
  autoRefresh: boolean
  refreshInterval: number
}

export interface SignalsConfig {
  minConfidence: number
  enabledIndicators: string[]
  timeframes: string[]
}

export interface AppConfig {
  mode: 'testnet' | 'mainnet'
  rpc: {
    testnet: string
    mainnet: string
  }
  trading: TradingConfig
  wallets: {
    testnet: WalletConfig[]
    mainnet: WalletConfig[]
  }
  selectedWallet: string | null
  notifications: NotificationConfig
  ui: UIConfig
  api: {
    coingecko: string
    solanaFm: string
  }
  signals: SignalsConfig
}

class ConfigManager {
  private config: AppConfig | null = null
  private readonly STORAGE_KEY = 'rizz_trader_config'

  constructor() {
    // Initialize with default config if none exists
    this.initializeConfig()
  }

  private initializeConfig() {
    if (typeof window === 'undefined') return // Server-side rendering guard
    
    const stored = localStorage.getItem(this.STORAGE_KEY)
    if (!stored) {
      // Set default config
      this.config = this.getDefaultConfig()
      this.saveConfigSync()
    }
  }

  private getDefaultConfig(): AppConfig {
    return {
      mode: 'testnet',
      rpc: {
        testnet: 'https://api.devnet.solana.com',
        mainnet: 'https://api.mainnet-beta.solana.com'
      },
      trading: {
        maxPositions: 5,
        maxUsdPerTrade: 100,
        stopLossPercent: 5,
        takeProfitPercent: 15,
        riskManagement: {
          maxDailyLoss: 500,
          maxDrawdown: 20
        }
      },
      wallets: {
        testnet: [],
        mainnet: []
      },
      selectedWallet: null,
      notifications: {
        trades: true,
        signals: true,
        errors: true
      },
      ui: {
        theme: 'dark',
        autoRefresh: true,
        refreshInterval: 30000
      },
      api: {
        coingecko: 'https://api.coingecko.com/api/v3',
        solanaFm: 'https://api.solana.fm'
      },
      signals: {
        minConfidence: 90,
        enabledIndicators: ['ema', 'rsi', 'macd', 'support_resistance'],
        timeframes: ['1m', '5m', '15m']
      }
    }
  }

  async loadConfig(): Promise<AppConfig> {
    if (typeof window === 'undefined') {
      // Server-side: return default config
      return this.getDefaultConfig()
    }
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.config = JSON.parse(stored)
        return this.config!
      } else {
        // Create default config
        this.config = this.getDefaultConfig()
        this.saveConfigSync()
        return this.config
      }
    } catch (error) {
      console.error('Failed to load config from localStorage:', error)
      // Fallback to default config
      this.config = this.getDefaultConfig()
      return this.config
    }
  }

  loadConfigSync(): AppConfig {
    if (typeof window === 'undefined') {
      // Server-side: return default config
      return this.getDefaultConfig()
    }
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY)
      if (stored) {
        this.config = JSON.parse(stored)
        return this.config!
      } else {
        // Create default config
        this.config = this.getDefaultConfig()
        this.saveConfigSync()
        return this.config
      }
    } catch (error) {
      console.error('Failed to load config from localStorage:', error)
      // Fallback to default config
      this.config = this.getDefaultConfig()
      return this.config
    }
  }

  async saveConfig(config: AppConfig): Promise<void> {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(config, null, 2))
      this.config = config
    } catch (error) {
      console.error('Failed to save config to localStorage:', error)
      throw error
    }
  }

  saveConfigSync(): void {
    if (typeof window === 'undefined' || !this.config) return
    
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(this.config, null, 2))
    } catch (error) {
      console.error('Failed to save config to localStorage:', error)
    }
  }

  async addWallet(wallet: WalletConfig): Promise<void> {
    const config = await this.loadConfig()
    
    if (wallet.type === 'testnet') {
      config.wallets.testnet.push(wallet)
    } else {
      config.wallets.mainnet.push(wallet)
    }
    
    // Set as selected wallet if it's the first one
    if (!config.selectedWallet) {
      config.selectedWallet = wallet.id
    }
    
    await this.saveConfig(config)
  }

  async removeWallet(walletId: string): Promise<void> {
    const config = await this.loadConfig()
    
    config.wallets.testnet = config.wallets.testnet.filter(w => w.id !== walletId)
    config.wallets.mainnet = config.wallets.mainnet.filter(w => w.id !== walletId)
    
    // Clear selected wallet if it was removed
    if (config.selectedWallet === walletId) {
      const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]
      config.selectedWallet = allWallets.length > 0 ? allWallets[0].id : null
    }
    
    await this.saveConfig(config)
  }

  async setSelectedWallet(walletId: string): Promise<void> {
    const config = await this.loadConfig()
    config.selectedWallet = walletId
    await this.saveConfig(config)
  }

  getCurrentRpcUrl(): string {
    const config = this.loadConfigSync()
    return config.mode === 'testnet' ? config.rpc.testnet : config.rpc.mainnet
  }

  getSelectedWallet(): WalletConfig | null {
    const config = this.loadConfigSync()
    if (!config.selectedWallet) return null
    
    const allWallets = [...config.wallets.testnet, ...config.wallets.mainnet]
    return allWallets.find(w => w.id === config.selectedWallet) || null
  }
}

const configManager = new ConfigManager()
export default configManager
